# Debug Firebase Storage Avatar Upload

## Kiểm tra nhanh

### 1. Kiểm tra Firebase Storage đã được cấu hình chưa
```dart
// Trong terminal Flutter, chạy:
flutter doctor -v
```

### 2. Kiểm tra Firebase project ID
```dart
// Trong lib/firebase_options.dart
storageBucket: 'moviefinder-98.firebasestorage.app'
```

### 3. Test upload avatar
1. Mở app
2. Đăng nhập
3. Vào Profile Edit
4. Chạm vào avatar
5. Chọn ảnh từ camera/gallery
6. Nhấn "Save Changes"

## Các lỗi thường gặp và cách sửa

### Lỗi 1: Permission denied
```
[firebase_storage/unauthorized] User does not have permission to access this object.
```

**C<PERSON>ch sửa:**
1. Kiểm tra Firebase Storage Rules
2. Deploy rules mới:
```bash
firebase deploy --only storage
```

### Lỗi 2: Network error
```
[firebase_storage/unknown] An unknown error occurred
```

**<PERSON><PERSON><PERSON> sửa:**
1. Ki<PERSON>m tra internet connection
2. Ki<PERSON>m tra Firebase project settings

### Lỗi 3: File too large
```
[firebase_storage/invalid-argument] File size exceeds maximum allowed size
```

**Cách sửa:**
- Ảnh đã được resize về 512x512, không nên có lỗi này

### Lỗi 4: Authentication required
```
[firebase_storage/unauthenticated] User is not authenticated
```

**Cách sửa:**
- Đảm bảo user đã đăng nhập
- Kiểm tra Firebase Auth token

## Debug steps

### 1. Kiểm tra Firebase Console
1. Truy cập https://console.firebase.google.com/
2. Chọn project `moviefinder-98`
3. Vào Storage tab
4. Kiểm tra có folder `user_avatars` không

### 2. Kiểm tra logs
```bash
# Trong terminal Flutter
flutter logs
```

### 3. Test với ảnh nhỏ
- Chọn ảnh có kích thước nhỏ (< 1MB)
- Kiểm tra format (JPG/PNG)

### 4. Kiểm tra Firebase Storage Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Cho phép đọc tất cả file
    match /{allPaths=**} {
      allow read: if true;
    }
    
    // Cho phép người dùng đã đăng nhập upload avatar
    match /user_avatars/{fileName} {
      allow write, delete: if request.auth != null;
    }
    
    // Cho phép admin và developer truy cập tất cả
    match /{allPaths=**} {
      allow write, delete: if request.auth != null;
    }
  }
}
```

## Thông báo thành công

Khi upload thành công, bạn sẽ thấy:
1. Snackbar "Đang tải lên..."
2. Snackbar "Đã tải ảnh đại diện lên thành công!"
3. Avatar mới hiển thị trong profile
4. URL mới được lưu trong Firestore

## Kiểm tra kết quả

### 1. Trong Firebase Storage Console
- Vào Storage > Files
- Kiểm tra folder `user_avatars`
- Thấy file mới với tên `avatar_[userId]_[timestamp].jpg`

### 2. Trong Firestore Console
- Vào Firestore Database
- Collection `users`
- Document của user
- Field `photoUrl` có URL mới từ Firebase Storage

### 3. Trong app
- Avatar hiển thị ảnh mới
- Reload app, ảnh vẫn hiển thị đúng

## Commands hữu ích

```bash
# Kiểm tra Firebase CLI
firebase --version

# Login Firebase
firebase login

# Chọn project
firebase use moviefinder-98

# Deploy Storage rules
firebase deploy --only storage

# Xem logs
firebase functions:log

# Test Firebase connection
firebase projects:list
```

## Liên hệ support

Nếu vẫn gặp lỗi, cung cấp thông tin:
1. Thông báo lỗi chính xác
2. Steps để reproduce lỗi
3. Screenshots nếu có
4. Flutter doctor output
5. Firebase project settings
