# Hướng dẫn Deploy Firebase Realtime Database Rules

## Vấn đề hiện tại
Lỗi: `Index not defined, add ".indexOn": "status", for path "/notifications"`

## Gi<PERSON>i pháp

### 1. **Cập nhật Database Rules**
File `database.rules.json` đã được cập nhật với các index cần thiết:

```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "false",

    "notifications": {
      ".read": "auth != null",
      ".write": "auth != null",
      ".indexOn": ["type", "priority", "createdAt", "expiresAt", "status", "isPublic", "scheduledAt"],
      "$notification_id": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    },

    "user_notifications": {
      ".read": "auth != null",
      ".write": "auth != null",
      ".indexOn": ["userId", "notificationId", "isRead", "isSeen", "isDeleted", "createdAt"],
      "$notificationKey": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    },

    "notification_settings": {
      ".read": "auth != null",
      ".write": "auth != null",
      "$userId": {
        ".read": "auth != null && auth.uid == $userId",
        ".write": "auth != null && auth.uid == $userId"
      }
    },

    "notification_stats": {
      ".read": "auth != null",
      ".write": "auth != null",
      "$dateKey": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    },

    "bug_reports": {
      ".read": "auth != null",
      ".write": "auth != null",
      ".indexOn": ["reportedBy", "status", "createdAt"],
      "$bugReportId": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    }
  }
}
```

### 2. **Deploy Rules**

#### Option A: Sử dụng Firebase CLI
```bash
# Cài đặt Firebase CLI nếu chưa có
npm install -g firebase-tools

# Login vào Firebase
firebase login

# Khởi tạo project (nếu chưa có firebase.json)
firebase init database

# Deploy rules
firebase deploy --only database
```

#### Option B: Sử dụng Firebase Console
1. Vào [Firebase Console](https://console.firebase.google.com/)
2. Chọn project của bạn
3. Vào **Realtime Database** > **Rules**
4. Copy nội dung từ `database.rules.json` và paste vào
5. Click **Publish**

### 3. **Kiểm tra Rules đã được deploy**

Sau khi deploy, kiểm tra trong Firebase Console:
- Vào **Realtime Database** > **Rules**
- Xem tab **Rules** có chứa các index mới không
- Test rules bằng **Rules Playground**

### 4. **Các Index đã thêm**

#### Cho `notifications`:
- `type`: Lọc theo loại thông báo
- `priority`: Lọc theo độ ưu tiên
- `createdAt`: Sắp xếp theo thời gian tạo
- `expiresAt`: Tìm thông báo hết hạn
- `status`: Tìm thông báo scheduled/active
- `isPublic`: Phân biệt public/private
- `scheduledAt`: Tìm thông báo cần kích hoạt

#### Cho `user_notifications`:
- `userId`: Lọc theo user
- `notificationId`: Tìm notification cụ thể
- `isRead`: Lọc đã đọc/chưa đọc
- `isSeen`: Lọc đã xem/chưa xem
- `isDeleted`: Lọc đã xóa/chưa xóa
- `createdAt`: Sắp xếp theo thời gian

#### Cho `bug_reports`:
- `reportedBy`: Lọc theo người báo cáo
- `status`: Lọc theo trạng thái
- `createdAt`: Sắp xếp theo thời gian

### 5. **Kiểm tra sau khi deploy**

Chạy lại app và kiểm tra:
```bash
flutter run
```

Xem console logs, không còn lỗi:
```
✅ NotificationSchedulerService: Processed scheduled notifications
✅ NotificationSchedulerService: Cleaned up expired notifications
```

### 6. **Troubleshooting**

#### Nếu vẫn có lỗi index:
1. Đảm bảo rules đã được deploy thành công
2. Restart app để clear cache
3. Kiểm tra Firebase project ID đúng không

#### Nếu rules deploy thất bại:
1. Kiểm tra syntax JSON
2. Đảm bảo có quyền admin trên Firebase project
3. Thử deploy từ Firebase Console thay vì CLI

#### Nếu performance vẫn chậm:
1. Kiểm tra số lượng data trong database
2. Xem có query nào không có index
3. Monitor trong Firebase Console > Performance

### 7. **Monitoring**

Sau khi deploy, monitor:
- **Firebase Console** > **Realtime Database** > **Usage**
- Xem **Read/Write operations**
- Kiểm tra **Performance metrics**

### 8. **Best Practices**

1. **Luôn test rules** trước khi deploy production
2. **Backup rules** trước khi thay đổi
3. **Monitor performance** sau khi deploy
4. **Review security rules** định kỳ

## Kết luận

Sau khi deploy rules mới:
- ✅ Scheduled notifications sẽ hoạt động không lỗi
- ✅ Performance queries sẽ tốt hơn
- ✅ Hệ thống notification hoạt động ổn định
- ✅ Không còn index warnings

Hãy deploy rules và test lại hệ thống!
