import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/realtime_notification_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/realtime_notification_model.dart';
import '../../utils/app_colors.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<RealtimeNotificationController>();
    final authController = Get.find<AuthController>();

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Obx(() => Text(
              'Thông báo (${controller.unreadCount})',
              style: GoogleFonts.mulish(
                fontWeight: FontWeight.bold,
              ),
            )),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          // Search button
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context, controller),
          ),
          // Settings button
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Get.toNamed('/notification_settings'),
          ),
          // More options
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value, controller),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'mark_all_read',
                enabled: controller.unreadCount > 0,
                child: const Row(
                  children: [
                    Icon(Icons.done_all),
                    SizedBox(width: 8),
                    Text('Đánh dấu tất cả đã đọc'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: 8),
                    Text('Làm mới'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'force_refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh_outlined),
                    SizedBox(width: 8),
                    Text('Force Refresh (Debug)'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'statistics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('Thống kê'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'bulk_actions',
                child: Row(
                  children: [
                    Icon(Icons.checklist),
                    SizedBox(width: 8),
                    Text('Thao tác hàng loạt'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Bộ lọc
            _buildFilterChips(controller),

            // Danh sách thông báo
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (controller.errorMessage.isNotEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.errorRed,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Có lỗi xảy ra',
                          style: GoogleFonts.mulish(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          controller.errorMessage,
                          style: GoogleFonts.mulish(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            controller.clearError();
                            controller.refreshNotifications();
                          },
                          child: const Text('Thử lại'),
                        ),
                      ],
                    ),
                  );
                }

                final notifications = controller.filteredNotifications;

                if (notifications.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.notifications_none,
                          size: 64,
                          color: AppColors.textTertiary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Không có thông báo',
                          style: GoogleFonts.mulish(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Bạn sẽ nhận được thông báo ở đây',
                          style: GoogleFonts.mulish(
                            fontSize: 14,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    final notification = notifications[index];

                    // Wrap notification item trong Obx để reactive với changes
                    return Obx(() {
                      // Tìm notification hiện tại từ controller để có data mới nhất
                      final currentNotifications =
                          controller.filteredNotifications;
                      final currentNotification =
                          currentNotifications.firstWhere(
                        (n) =>
                            n.notification.id == notification.notification.id,
                        orElse: () =>
                            notification, // fallback nếu không tìm thấy
                      );

                      return _buildNotificationItem(
                        context,
                        currentNotification,
                        controller,
                        authController,
                      );
                    });
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChips(RealtimeNotificationController controller) {
    final filters = [
      {'key': 'all', 'label': 'Tất cả'},
      {'key': 'unread', 'label': 'Chưa đọc'},
      {'key': 'read', 'label': 'Đã đọc'},
      {'key': 'system', 'label': 'Hệ thống'},
      {'key': 'movie', 'label': 'Phim'},
      {'key': 'promo', 'label': 'Khuyến mãi'},
      {'key': 'ticket', 'label': 'Vé'},
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          return Obx(() => Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(filter['label']!),
                  selected: controller.selectedFilter == filter['key'],
                  onSelected: (selected) {
                    if (selected) {
                      controller.setFilter(filter['key']!);
                    }
                  },
                  selectedColor: AppColors.buttonPrimary,
                  checkmarkColor: AppColors.textPrimary,
                  labelStyle: GoogleFonts.mulish(
                    color: controller.selectedFilter == filter['key']
                        ? AppColors.textPrimary
                        : AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ));
        },
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    NotificationViewModel notification,
    RealtimeNotificationController controller,
    AuthController authController,
  ) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    // Xác định icon và màu sắc dựa trên loại thông báo
    IconData iconData = Icons.notifications;
    Color iconColor = AppColors.infoBlue;

    switch (notification.notification.type) {
      case 'movie':
        iconData = Icons.movie;
        iconColor = AppColors.screenPremium;
        break;
      case 'ticket':
        iconData = Icons.confirmation_number;
        iconColor = AppColors.successGreen;
        break;
      case 'promo':
        iconData = Icons.local_offer;
        iconColor = AppColors.warningOrange;
        break;
      case 'bug_report':
        iconData = Icons.bug_report;
        iconColor = AppColors.errorRed;
        break;
      default:
        iconData = Icons.info;
        iconColor = AppColors.infoBlue;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: notification.isRead ? 1 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: notification.isRead
            ? BorderSide.none
            : BorderSide(color: iconColor.withOpacity(0.3), width: 1),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () async {
          print('🔔 Notification tapped: ${notification.notification.id}');
          print('🔔 Current read status: ${notification.isRead}');
          print('🔔 Current seen status: ${notification.isSeen}');

          // Hiển thị chi tiết ngay lập tức
          _showNotificationDetails(context, notification, controller);

          // Đánh dấu đã đọc và đã xem trong background
          if (!notification.isRead) {
            print('🔔 Marking as read...');
            final success =
                await controller.markAsRead(notification.notification.id);
            print('🔔 Mark as read result: $success');
          }
          if (!notification.isSeen) {
            print('🔔 Marking as seen...');
            final success =
                await controller.markAsSeen(notification.notification.id);
            print('🔔 Mark as seen result: $success');
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  iconData,
                  color: iconColor,
                  size: 24,
                ),
              ),

              const SizedBox(width: 12),

              // Nội dung
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tiêu đề và trạng thái
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.notification.title,
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: notification.isRead
                                  ? FontWeight.w600
                                  : FontWeight.bold,
                              color: notification.isRead
                                  ? AppColors.textSecondary
                                  : AppColors.textPrimary,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: iconColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    // Nội dung
                    Text(
                      notification.notification.body,
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // Thời gian và độ ưu tiên
                    Row(
                      children: [
                        Text(
                          dateFormat.format(
                              notification.notification.createdAtDateTime),
                          style: GoogleFonts.mulish(
                            fontSize: 12,
                            color: AppColors.textTertiary,
                          ),
                        ),
                        if (notification.notification.priority != 'normal') ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(
                                  notification.notification.priority),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _getPriorityLabel(
                                  notification.notification.priority),
                              style: GoogleFonts.mulish(
                                fontSize: 10,
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'urgent':
        return AppColors.errorRed;
      case 'high':
        return AppColors.warningOrange;
      case 'low':
        return AppColors.successGreen;
      default:
        return AppColors.infoBlue;
    }
  }

  String _getPriorityLabel(String priority) {
    switch (priority) {
      case 'urgent':
        return 'KHẨN';
      case 'high':
        return 'CAO';
      case 'low':
        return 'THẤP';
      default:
        return 'BÌNH THƯỜNG';
    }
  }

  void _showNotificationDetails(
    BuildContext context,
    NotificationViewModel notification,
    RealtimeNotificationController controller,
  ) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    // Xác định icon và màu sắc dựa trên loại thông báo
    IconData iconData = Icons.notifications;
    Color iconColor = AppColors.infoBlue;

    switch (notification.notification.type) {
      case 'movie':
        iconData = Icons.movie;
        iconColor = AppColors.screenPremium;
        break;
      case 'ticket':
        iconData = Icons.confirmation_number;
        iconColor = AppColors.successGreen;
        break;
      case 'promo':
        iconData = Icons.local_offer;
        iconColor = AppColors.warningOrange;
        break;
      case 'bug_report':
        iconData = Icons.bug_report;
        iconColor = AppColors.errorRed;
        break;
      default:
        iconData = Icons.info;
        iconColor = AppColors.infoBlue;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.borderPrimary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(28),
                      ),
                      child: Icon(
                        iconData,
                        color: iconColor,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            notification.notification.title,
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            dateFormat.format(
                                notification.notification.createdAtDateTime),
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // Nội dung
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Nội dung chính
                      Text(
                        notification.notification.body,
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          height: 1.5,
                          color: AppColors.textPrimary,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Thông tin bổ sung
                      if (notification.notification.imageUrl != null) ...[
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            notification.notification.imageUrl!,
                            width: double.infinity,
                            height: 200,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: double.infinity,
                                height: 200,
                                decoration: BoxDecoration(
                                  color: AppColors.surfaceColor,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.image_not_supported,
                                  size: 48,
                                  color: AppColors.textTertiary,
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],

                      // Thông tin chi tiết
                      _buildDetailRow('Loại',
                          _getTypeLabel(notification.notification.type)),
                      _buildDetailRow(
                          'Độ ưu tiên',
                          _getPriorityLabel(
                              notification.notification.priority)),
                      _buildDetailRow('Trạng thái',
                          notification.isRead ? 'Đã đọc' : 'Chưa đọc'),
                      if (notification.readAt != null)
                        _buildDetailRow(
                            'Đọc lúc', dateFormat.format(notification.readAt!)),
                      if (notification.notification.expiresAtDateTime
                          .isAfter(DateTime.now()))
                        _buildDetailRow(
                            'Hết hạn',
                            dateFormat.format(
                                notification.notification.expiresAtDateTime)),
                    ],
                  ),
                ),
              ),

              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: AppColors.surfaceColor,
                  border: Border(
                    top: BorderSide(color: AppColors.borderPrimary),
                  ),
                ),
                child: Row(
                  children: [
                    // Nút xóa
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // Đóng modal trước
                          Navigator.pop(context);

                          // Xóa thông báo
                          controller
                              .deleteNotification(
                            notification.notification.id,
                          )
                              .then((success) {
                            if (success) {
                              Get.snackbar(
                                'Thành công',
                                'Đã xóa thông báo',
                                snackPosition: SnackPosition.BOTTOM,
                                backgroundColor: AppColors.successGreen,
                                colorText: AppColors.textPrimary,
                              );
                            }
                          });
                        },
                        icon: const Icon(Icons.delete_outline),
                        label: Text(
                          'Xóa',
                          style: GoogleFonts.mulish(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          foregroundColor: AppColors.errorRed,
                          side: const BorderSide(color: AppColors.errorRed),
                        ),
                      ),
                    ),

                    // Nút xem chi tiết cho các thông báo có targetScreen
                    if (notification.notification.targetScreen != null &&
                        notification.notification.targetScreen !=
                            'bug_report') ...[
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            // Điều hướng đến màn hình mục tiêu
                            Get.toNamed(
                              notification.notification.targetScreen!,
                              parameters: notification.notification.data?.map(
                                      (key, value) =>
                                          MapEntry(key, value.toString())) ??
                                  {},
                            );
                          },
                          icon: const Icon(Icons.open_in_new),
                          label: Text(
                            'Xem chi tiết',
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            backgroundColor: iconColor,
                            foregroundColor: AppColors.textPrimary,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: GoogleFonts.mulish(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.mulish(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(String type) {
    switch (type) {
      case 'movie':
        return 'Phim';
      case 'ticket':
        return 'Vé';
      case 'promo':
        return 'Khuyến mãi';
      case 'bug_report':
        return 'Báo lỗi';
      default:
        return 'Hệ thống';
    }
  }

  // Handle menu actions
  void _handleMenuAction(
      String action, RealtimeNotificationController controller) {
    switch (action) {
      case 'mark_all_read':
        _markAllAsRead(controller);
        break;
      case 'refresh':
        _refreshNotifications(controller);
        break;
      case 'force_refresh':
        _forceRefreshNotifications(controller);
        break;
      case 'statistics':
        _showStatistics(controller);
        break;
      case 'bulk_actions':
        _showBulkActionsDialog(controller);
        break;
    }
  }

  // Mark all as read
  void _markAllAsRead(RealtimeNotificationController controller) async {
    final success = await controller.markAllAsRead();
    if (success) {
      Get.snackbar(
        'Thành công',
        'Đã đánh dấu tất cả thông báo đã đọc',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.successGreen,
        colorText: AppColors.textPrimary,
      );
    }
  }

  // Refresh notifications
  void _refreshNotifications(RealtimeNotificationController controller) {
    controller.refreshNotifications();
    Get.snackbar(
      'Làm mới',
      'Đang làm mới thông báo...',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Force refresh notifications (debug)
  void _forceRefreshNotifications(RealtimeNotificationController controller) {
    print('🔄 Force refresh triggered');

    try {
      // Safer approach - just refresh without disposing
      controller.refreshNotifications();

      // Force UI update
      controller.update();

      Get.snackbar(
        'Force Refresh',
        'Đã force refresh thông báo và streams',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.warningOrange,
        colorText: AppColors.textPrimary,
      );
    } catch (e) {
      print('🔄 Force refresh error: $e');
      Get.snackbar(
        'Force Refresh Error',
        'Lỗi khi force refresh: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.errorRed,
        colorText: AppColors.textPrimary,
      );
    }
  }

  // Show search dialog
  void _showSearchDialog(
      BuildContext context, RealtimeNotificationController controller) {
    final searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Tìm kiếm thông báo',
          style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
        ),
        content: TextField(
          controller: searchController,
          decoration: const InputDecoration(
            hintText: 'Nhập từ khóa tìm kiếm...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              if (searchController.text.trim().isNotEmpty) {
                _performSearch(searchController.text.trim(), controller);
              }
            },
            child: const Text('Tìm kiếm'),
          ),
        ],
      ),
    );
  }

  // Perform search
  void _performSearch(String query, RealtimeNotificationController controller) {
    final results = controller.searchNotifications(query);

    if (results.isEmpty) {
      Get.snackbar(
        'Không tìm thấy',
        'Không có thông báo nào phù hợp với từ khóa "$query"',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      Get.snackbar(
        'Kết quả tìm kiếm',
        'Tìm thấy ${results.length} thông báo phù hợp',
        snackPosition: SnackPosition.BOTTOM,
      );
      // TODO: Show search results in a separate screen or filter current list
    }
  }

  // Show statistics dialog
  void _showStatistics(RealtimeNotificationController controller) {
    final typeStats = controller.getNotificationStatsByType();
    final priorityStats = controller.getNotificationStatsByPriority();
    final readRate = controller.getReadRate();

    showDialog(
      context: Get.context!,
      builder: (context) => AlertDialog(
        title: Text(
          'Thống kê thông báo',
          style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tổng quan',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text('Tổng số thông báo: ${controller.notifications.length}'),
              Text('Chưa đọc: ${controller.unreadCount}'),
              Text('Tỷ lệ đã đọc: ${(readRate * 100).toStringAsFixed(1)}%'),
              const SizedBox(height: 16),
              Text(
                'Theo loại',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...typeStats.entries.map((entry) =>
                  Text('${_getTypeLabel(entry.key)}: ${entry.value}')),
              const SizedBox(height: 16),
              Text(
                'Theo độ ưu tiên',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...priorityStats.entries.map((entry) =>
                  Text('${_getPriorityLabel(entry.key)}: ${entry.value}')),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  // Show bulk actions dialog
  void _showBulkActionsDialog(RealtimeNotificationController controller) {
    showDialog(
      context: Get.context!,
      builder: (context) => AlertDialog(
        title: Text(
          'Thao tác hàng loạt',
          style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.done_all),
              title: const Text('Đánh dấu tất cả đã đọc'),
              onTap: () {
                Navigator.pop(context);
                _markAllAsRead(controller);
              },
            ),
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('Đánh dấu tất cả đã xem'),
              onTap: () async {
                Navigator.pop(context);
                final success = await controller.markAllAsSeen();
                if (success) {
                  Get.snackbar(
                    'Thành công',
                    'Đã đánh dấu tất cả thông báo đã xem',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete_sweep),
              title: const Text('Xóa thông báo đã đọc'),
              onTap: () {
                Navigator.pop(context);
                _deleteReadNotifications(controller);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
        ],
      ),
    );
  }

  // Delete read notifications
  void _deleteReadNotifications(
      RealtimeNotificationController controller) async {
    final readNotifications = controller.readNotifications;

    if (readNotifications.isEmpty) {
      Get.snackbar(
        'Thông báo',
        'Không có thông báo đã đọc nào để xóa',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: Text(
          'Xác nhận xóa',
          style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa ${readNotifications.length} thông báo đã đọc?',
          style: GoogleFonts.mulish(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final notificationIds =
          readNotifications.map((n) => n.notification.id).toList();

      final success =
          await controller.deleteMultipleNotifications(notificationIds);

      if (success) {
        Get.snackbar(
          'Thành công',
          'Đã xóa ${readNotifications.length} thông báo',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.successGreen,
          colorText: AppColors.textPrimary,
        );
      }
    }
  }
}
