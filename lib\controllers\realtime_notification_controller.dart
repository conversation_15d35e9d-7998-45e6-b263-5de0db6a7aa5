import 'dart:async';
import 'package:get/get.dart';
import '../models/realtime_notification_model.dart';
import '../models/notification_settings_model.dart';
import '../services/realtime_database_service.dart';
import 'auth_controller.dart';
import 'notification_settings_controller.dart';

class RealtimeNotificationController extends GetxController {
  final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();
  final AuthController _authController = Get.find<AuthController>();

  // Lazy initialization for settings controller
  NotificationSettingsController? _settingsController;

  // Observables
  final RxList<NotificationViewModel> _notifications =
      <NotificationViewModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxInt _unreadCount = 0.obs;
  final RxInt _unseenCount = 0.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _selectedFilter = 'all'.obs;
  final RxBool _enableRealTimeUpdates = true.obs;
  final RxInt _dailyNotificationCount = 0.obs;

  // Streams
  StreamSubscription? _publicNotificationsSubscription;
  StreamSubscription? _userNotificationsSubscription;
  Timer? _scheduledNotificationTimer;
  Timer? _cleanupTimer;

  // Getters
  List<NotificationViewModel> get notifications => _notifications;
  List<NotificationViewModel> get filteredNotifications {
    switch (_selectedFilter.value) {
      case 'unread':
        return _notifications.where((n) => !n.isRead).toList();
      case 'read':
        return _notifications.where((n) => n.isRead).toList();
      case 'system':
        return _notifications
            .where((n) => n.notification.type == 'system')
            .toList();
      case 'movie':
        return _notifications
            .where((n) => n.notification.type == 'movie')
            .toList();
      case 'promo':
        return _notifications
            .where((n) => n.notification.type == 'promo')
            .toList();
      case 'ticket':
        return _notifications
            .where((n) => n.notification.type == 'ticket')
            .toList();
      default:
        return _notifications;
    }
  }

  bool get isLoading => _isLoading.value;
  int get unreadCount => _unreadCount.value;
  int get unseenCount => _unseenCount.value;
  String get errorMessage => _errorMessage.value;
  String get selectedFilter => _selectedFilter.value;

  @override
  void onInit() {
    super.onInit();
    _setupAuthListener();
    initializeEnhancedFeatures();
  }

  @override
  void onClose() {
    _cancelSubscriptions();
    _scheduledNotificationTimer?.cancel();
    _cleanupTimer?.cancel();
    _realtimeService.dispose();
    super.onClose();
  }

  // Thiết lập lắng nghe sự thay đổi đăng nhập
  void _setupAuthListener() {
    ever(_authController.userRx, (_) {
      _cancelSubscriptions();
      if (_authController.user != null) {
        _fetchNotifications();
      } else {
        _notifications.clear();
        _unreadCount.value = 0;
      }
    });

    // Fetch ngay lập tức nếu đã đăng nhập
    if (_authController.user != null) {
      _fetchNotifications();
    }
  }

  // Hủy các subscription
  void _cancelSubscriptions() {
    _publicNotificationsSubscription?.cancel();
    _userNotificationsSubscription?.cancel();
    _publicNotificationsSubscription = null;
    _userNotificationsSubscription = null;
  }

  // Lấy thông báo
  void _fetchNotifications() {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Lấy thông báo công khai
      _publicNotificationsSubscription = _realtimeService
          .getPublicNotificationsStream()
          .listen(_handlePublicNotifications, onError: _handleError);

      // Lấy thông báo của người dùng
      if (_authController.user?.id != null) {
        _userNotificationsSubscription = _realtimeService
            .getUserNotificationsStream(_authController.user!.id!)
            .listen(_handleUserNotificationsWithState, onError: _handleError);
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải thông báo: $e';
      _isLoading.value = false;
    }
  }

  // Xử lý thông báo công khai
  void _handlePublicNotifications(
      List<RealtimeNotificationModel> publicNotifications) {
    print('Received ${publicNotifications.length} public notifications');

    // Lọc thông báo admin nếu không phải admin
    final filteredPublicNotifications =
        publicNotifications.where((notification) {
      final isAdminNotification =
          notification.data?['isAdminNotification'] == 'true';
      return !isAdminNotification || (_authController.user?.isAdmin ?? false);
    }).toList();

    // Chuyển đổi thành NotificationViewModel
    final viewModels = filteredPublicNotifications
        .map(
            (notification) => NotificationViewModel(notification: notification))
        .toList();

    // Cập nhật danh sách thông báo
    _updateNotifications(viewModels, isPublic: true);
  }

  // Xử lý thông báo của người dùng với trạng thái
  void _handleUserNotificationsWithState(
      List<NotificationViewModel> userNotifications) {
    print('🔄 Received ${userNotifications.length} user notifications');

    // Debug: Log read/unread counts from stream
    final unreadCount = userNotifications.where((n) => !n.isRead).length;
    final readCount = userNotifications.where((n) => n.isRead).length;
    print('🔄 From stream - Read: $readCount, Unread: $unreadCount');

    // Debug: Log first few notifications status from stream
    for (int i = 0; i < userNotifications.length && i < 3; i++) {
      final n = userNotifications[i];
      print(
          '🔄 Stream notification ${i + 1}: ${n.notification.id.substring(0, 8)}... - Read: ${n.isRead}, Seen: ${n.isSeen}');
    }

    _updateNotifications(userNotifications, isPublic: false);
  }

  // Cập nhật danh sách thông báo
  void _updateNotifications(List<NotificationViewModel> newNotifications,
      {required bool isPublic}) {
    print(
        '🔄 Updating notifications - isPublic: $isPublic, count: ${newNotifications.length}');

    if (isPublic) {
      // Xóa tất cả public notifications cũ
      _notifications.removeWhere((notification) =>
          notification.notification.isPublic &&
          (notification.notification.targetUserIds == null ||
              notification.notification.targetUserIds!.isEmpty));
      print(
          '🔄 Removed old public notifications, remaining: ${_notifications.length}');

      // Thêm public notifications mới
      _notifications.addAll(newNotifications);
    } else {
      // Với user notifications, cần merge thông minh để tránh duplicate
      print('🔄 Merging user notifications...');

      for (var newNotification in newNotifications) {
        // Tìm xem đã có notification này chưa
        final existingIndex = _notifications.indexWhere((existing) =>
            existing.notification.id == newNotification.notification.id);

        if (existingIndex != -1) {
          // Update existing notification với data mới nhất
          print(
              '🔄 Updating existing notification: ${newNotification.notification.id.substring(0, 8)}...');
          _notifications[existingIndex] = newNotification;
        } else {
          // Thêm notification mới
          print(
              '🔄 Adding new notification: ${newNotification.notification.id.substring(0, 8)}...');
          _notifications.add(newNotification);
        }
      }
    }

    print('🔄 Final notification count: ${_notifications.length}');

    // Sắp xếp theo thời gian
    _notifications.sort(
        (a, b) => b.notification.createdAt.compareTo(a.notification.createdAt));

    // Cập nhật số lượng thông báo chưa đọc và chưa xem
    _updateCounts();

    print(
        '🔄 Final counts - Unread: ${_unreadCount.value}, Unseen: ${_unseenCount.value}');

    _isLoading.value = false;
  }

  // Cập nhật số lượng thông báo chưa đọc và chưa xem
  void _updateCounts() {
    _unreadCount.value = _notifications.where((n) => !n.isRead).length;
    _unseenCount.value = _notifications.where((n) => !n.isSeen).length;
  }

  // Xử lý lỗi
  void _handleError(dynamic error) {
    print('Error in notification stream: $error');
    _errorMessage.value = 'Lỗi khi tải thông báo: $error';
    _isLoading.value = false;
  }

  // Đánh dấu thông báo đã đọc
  Future<bool> markAsRead(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.markNotificationAsRead(
          _authController.user!.id!, notificationId);

      // KHÔNG cập nhật local state ở đây
      // Stream sẽ tự động emit update mới từ database
      // Điều này tránh duplicate

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã đọc: $e';
      return false;
    }
  }

  // Đánh dấu thông báo đã xem
  Future<bool> markAsSeen(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.markNotificationAsSeen(
          _authController.user!.id!, notificationId);

      // KHÔNG cập nhật local state ở đây
      // Stream sẽ tự động emit update mới từ database
      // Điều này tránh duplicate

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã xem: $e';
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã đọc
  Future<bool> markAllAsRead() async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService
          .markAllNotificationsAsRead(_authController.user!.id!);

      // KHÔNG cập nhật local state ở đây
      // Stream sẽ tự động emit update mới từ database
      // Điều này tránh duplicate

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã đọc: $e';
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã xem
  Future<bool> markAllAsSeen() async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService
          .markAllNotificationsAsSeen(_authController.user!.id!);

      // KHÔNG cập nhật local state ở đây
      // Stream sẽ tự động emit update mới từ database
      // Điều này tránh duplicate

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã xem: $e';
      return false;
    }
  }

  // Xóa thông báo
  Future<bool> deleteNotification(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.deleteNotification(
          _authController.user!.id!, notificationId);

      if (success) {
        // Cập nhật local state
        _notifications.removeWhere(
            (notification) => notification.notification.id == notificationId);
        _updateCounts();
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi xóa thông báo: $e';
      return false;
    }
  }

  // Làm mới thông báo
  void refreshNotifications() {
    _cancelSubscriptions();
    _notifications.clear();
    _fetchNotifications();
  }

  // Thay đổi bộ lọc
  void setFilter(String filter) {
    _selectedFilter.value = filter;
  }

  // Tạo thông báo mới (chỉ admin/developer)
  Future<String?> createNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = true,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
  }) async {
    try {
      return await _realtimeService.createNotification(
        title: title,
        body: body,
        imageUrl: imageUrl,
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
        type: type,
        priority: priority,
        expiresIn: expiresIn,
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo thông báo: $e';
      return null;
    }
  }

  // Lấy thông báo theo loại
  List<NotificationViewModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.notification.type == type).toList();
  }

  // Lấy thông báo chưa đọc
  List<NotificationViewModel> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // Lấy thông báo đã đọc
  List<NotificationViewModel> get readNotifications {
    return _notifications.where((n) => n.isRead).toList();
  }

  // Lấy thông báo theo độ ưu tiên
  List<NotificationViewModel> getNotificationsByPriority(String priority) {
    return _notifications
        .where((n) => n.notification.priority == priority)
        .toList();
  }

  // Kiểm tra có thông báo mới không
  bool get hasNewNotifications => _unseenCount.value > 0;

  // Xóa lỗi
  void clearError() {
    _errorMessage.value = '';
  }

  // ==================== ENHANCED NOTIFICATION METHODS ====================

  // Get settings controller (lazy initialization)
  NotificationSettingsController get settingsController {
    _settingsController ??= Get.find<NotificationSettingsController>();
    return _settingsController!;
  }

  // Enhanced notification creation with settings check
  Future<String?> createEnhancedNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = false,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
    DateTime? scheduledAt,
    List<String>? tags,
    Map<String, dynamic>? actionButtons,
  }) async {
    try {
      return await _realtimeService.createEnhancedNotification(
        title: title,
        body: body,
        imageUrl: imageUrl,
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
        type: type,
        priority: priority,
        expiresIn: expiresIn,
        scheduledAt: scheduledAt,
        tags: tags,
        actionButtons: actionButtons,
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo thông báo nâng cao: $e';
      return null;
    }
  }

  // Start scheduled notification processing
  void startScheduledNotificationProcessing() {
    _scheduledNotificationTimer?.cancel();
    _scheduledNotificationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _realtimeService.processScheduledNotifications(),
    );
  }

  // Start cleanup timer
  void startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 6),
      (_) => _realtimeService.cleanupExpiredNotifications(),
    );
  }

  // Toggle real-time updates
  void toggleRealTimeUpdates() {
    _enableRealTimeUpdates.value = !_enableRealTimeUpdates.value;

    if (_enableRealTimeUpdates.value) {
      _fetchNotifications();
    } else {
      _cancelSubscriptions();
    }
  }

  // Get daily notification count
  int get dailyNotificationCount => _dailyNotificationCount.value;

  // Reset daily notification count
  void resetDailyNotificationCount() {
    _dailyNotificationCount.value = 0;
  }

  // Increment daily notification count
  void incrementDailyNotificationCount() {
    _dailyNotificationCount.value++;
  }

  // Bulk operations
  Future<bool> markMultipleAsRead(List<String> notificationIds) async {
    if (_authController.user?.id == null) return false;

    try {
      bool allSuccess = true;
      for (String notificationId in notificationIds) {
        final success = await _realtimeService.markNotificationAsRead(
          _authController.user!.id!,
          notificationId,
        );
        if (!success) allSuccess = false;
      }
      return allSuccess;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu nhiều thông báo: $e';
      return false;
    }
  }

  Future<bool> deleteMultipleNotifications(List<String> notificationIds) async {
    if (_authController.user?.id == null) return false;

    try {
      bool allSuccess = true;
      for (String notificationId in notificationIds) {
        final success = await _realtimeService.deleteNotification(
          _authController.user!.id!,
          notificationId,
        );
        if (!success) allSuccess = false;
      }

      if (allSuccess) {
        // Update local state
        _notifications.removeWhere((notification) =>
            notificationIds.contains(notification.notification.id));
        _updateCounts();
      }

      return allSuccess;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi xóa nhiều thông báo: $e';
      return false;
    }
  }

  // Advanced filtering
  List<NotificationViewModel> getNotificationsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return _notifications.where((notification) {
      final createdAt = notification.notification.createdAtDateTime;
      return createdAt.isAfter(startDate) && createdAt.isBefore(endDate);
    }).toList();
  }

  List<NotificationViewModel> searchNotifications(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _notifications.where((notification) {
      return notification.notification.title
              .toLowerCase()
              .contains(lowercaseQuery) ||
          notification.notification.body.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Notification statistics
  Map<String, int> getNotificationStatsByType() {
    final stats = <String, int>{};
    for (var notification in _notifications) {
      final type = notification.notification.type;
      stats[type] = (stats[type] ?? 0) + 1;
    }
    return stats;
  }

  Map<String, int> getNotificationStatsByPriority() {
    final stats = <String, int>{};
    for (var notification in _notifications) {
      final priority = notification.notification.priority;
      stats[priority] = (stats[priority] ?? 0) + 1;
    }
    return stats;
  }

  double getReadRate() {
    if (_notifications.isEmpty) return 0.0;
    final readCount = _notifications.where((n) => n.isRead).length;
    return readCount / _notifications.length;
  }

  // Initialize enhanced features
  void initializeEnhancedFeatures() {
    startScheduledNotificationProcessing();
    startCleanupTimer();
  }
}
