import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Kiểm tra và yêu cầu quyền camera
  Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        final result = await Permission.camera.request();
        return result.isGranted;
      }

      if (status.isPermanentlyDenied) {
        _showPermissionDialog(
          'Quyền Camera',
          'Ứng dụng cần quyền truy cập camera để chụp ảnh. Vui lòng cấp quyền trong Settings.',
        );
        return false;
      }

      return false;
    } catch (e) {
      print('Error requesting camera permission: $e');
      return false;
    }
  }

  /// Kiểm tra và yêu cầu quyền storage
  Future<bool> requestStoragePermission() async {
    try {
      // Cho Android 13+ (API 33+), sử dụng photos permission
      Permission permission = Permission.photos;

      // Cho Android 12 và thấp hơn, sử dụng storage permission
      if (await _isAndroid12OrLower()) {
        permission = Permission.storage;
      }

      final status = await permission.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        final result = await permission.request();
        return result.isGranted;
      }

      if (status.isPermanentlyDenied) {
        _showPermissionDialog(
          'Quyền Thư viện ảnh',
          'Ứng dụng cần quyền truy cập thư viện ảnh để chọn ảnh. Vui lòng cấp quyền trong Settings.',
        );
        return false;
      }

      return false;
    } catch (e) {
      print('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Kiểm tra và yêu cầu tất cả quyền cần thiết cho image picker
  Future<bool> requestImagePickerPermissions() async {
    try {
      print('🔍 Checking image picker permissions...');

      // Kiểm tra quyền camera
      final cameraStatus = await Permission.camera.status;
      print('📷 Camera permission status: $cameraStatus');

      // Kiểm tra quyền storage/photos
      final storageStatus = await Permission.storage.status;
      final photosStatus = await Permission.photos.status;
      print('📁 Storage permission status: $storageStatus');
      print('🖼️ Photos permission status: $photosStatus');

      bool cameraGranted = cameraStatus.isGranted;
      bool storageGranted = storageStatus.isGranted || photosStatus.isGranted;

      // Nếu chưa có quyền camera, yêu cầu
      if (!cameraGranted) {
        print('🔄 Requesting camera permission...');
        final cameraResult = await Permission.camera.request();
        cameraGranted = cameraResult.isGranted;
        print('📷 Camera permission result: $cameraResult');
      }

      // Nếu chưa có quyền storage, yêu cầu
      if (!storageGranted) {
        print('🔄 Requesting storage/photos permission...');

        // Thử photos trước (cho Android 13+)
        final photosResult = await Permission.photos.request();
        if (photosResult.isGranted) {
          storageGranted = true;
          print('🖼️ Photos permission granted: $photosResult');
        } else {
          // Nếu photos không được, thử storage (cho Android 12-)
          final storageResult = await Permission.storage.request();
          storageGranted = storageResult.isGranted;
          print('📁 Storage permission result: $storageResult');
        }
      }

      final result = cameraGranted && storageGranted;
      print(
          '✅ Final permission result: Camera=$cameraGranted, Storage=$storageGranted, Overall=$result');

      return result;
    } catch (e) {
      print('❌ Error requesting image picker permissions: $e');
      return false;
    }
  }

  /// Kiểm tra xem có phải Android 12 hoặc thấp hơn không
  Future<bool> _isAndroid12OrLower() async {
    try {
      // Sử dụng permission để kiểm tra version
      final manageExternalStorage =
          await Permission.manageExternalStorage.status;
      // Nếu permission này tồn tại thì là Android 11+
      return manageExternalStorage == PermissionStatus.denied;
    } catch (e) {
      return true; // Mặc định là Android thấp hơn
    }
  }

  /// Hiển thị dialog yêu cầu mở settings
  void _showPermissionDialog(String title, String message) {
    Get.dialog(
      AlertDialog(
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        content: Text(
          message,
          style: const TextStyle(color: Colors.white70),
        ),
        backgroundColor: const Color(0xFF1a1a1a),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'Hủy',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
            child: const Text('Mở Settings'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Kiểm tra trạng thái quyền hiện tại
  Future<Map<String, bool>> checkAllPermissions() async {
    try {
      final camera = await Permission.camera.status;
      final storage = await Permission.storage.status;
      final photos = await Permission.photos.status;

      return {
        'camera': camera.isGranted,
        'storage': storage.isGranted,
        'photos': photos.isGranted,
      };
    } catch (e) {
      print('Error checking permissions: $e');
      return {
        'camera': false,
        'storage': false,
        'photos': false,
      };
    }
  }

  /// Hiển thị thông tin quyền cho debug
  Future<void> showPermissionStatus() async {
    final permissions = await checkAllPermissions();

    Get.snackbar(
      'Trạng thái quyền',
      'Camera: ${(permissions['camera'] ?? false) ? '✅' : '❌'}\n'
          'Storage: ${(permissions['storage'] ?? false) ? '✅' : '❌'}\n'
          'Photos: ${(permissions['photos'] ?? false) ? '✅' : '❌'}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
    );
  }
}
