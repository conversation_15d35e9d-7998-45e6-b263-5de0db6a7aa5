# Hệ thống thông báo Realtime - Đớp Phim

## Tổng quan

Hệ thống thông báo realtime mới đã được xây dựng hoàn toàn từ đầu để thay thế hệ thống cũ, sử dụng Firebase Realtime Database để đảm bảo tính realtime và hiệu suất cao.

## Tính năng chính

### 1. Thông báo Realtime
- **Stream updates**: Thông bá<PERSON> được cập nhật realtime qua Firebase Realtime Database
- **Trạng thái đọc/chưa đọc**: <PERSON> dõi trạng thái đọc của từng người dùng
- **Đếm số thông báo chưa đọc**: Hiển thị badge số lượng thông báo chưa đọc
- **Tự động làm mới**: Dữ liệu đượ<PERSON> cập nhật tự động không cần reload

### 2. <PERSON><PERSON> loại thông báo
- **System**: <PERSON>h<PERSON><PERSON> báo hệ thống
- **Movie**: Th<PERSON><PERSON> báo về phim mới, lị<PERSON> chiếu
- **Promo**: Thông báo khuyến mãi, ưu đãi
- **Ticket**: Thông báo về vé, nhắc nhở hết hạn
- **Bug Report**: Thông báo báo lỗi (dành cho admin/developer)

### 3. Độ ưu tiên
- **Low**: Ưu tiên thấp (màu xanh lá)
- **Normal**: Ưu tiên bình thường (màu xanh dương)
- **High**: Ưu tiên cao (màu cam)
- **Urgent**: Khẩn cấp (màu đỏ)

### 4. Bộ lọc thông báo
- **Tất cả**: Hiển thị tất cả thông báo
- **Chưa đọc**: Chỉ hiển thị thông báo chưa đọc
- **Đã đọc**: Chỉ hiển thị thông báo đã đọc
- **Theo loại**: Lọc theo system, movie, promo, ticket

## Cấu trúc hệ thống

### Models
- **RealtimeNotificationModel**: Model chính cho thông báo
- **RealtimeUserNotificationModel**: Model trạng thái thông báo của user
- **NotificationViewModel**: Model kết hợp để hiển thị UI

### Controllers
- **RealtimeNotificationController**: Controller chính quản lý thông báo
  - Fetch notifications từ Realtime Database
  - Quản lý trạng thái đọc/chưa đọc
  - Xử lý bộ lọc và tìm kiếm
  - Tạo thông báo mới (admin/developer)

### Services
- **RealtimeDatabaseService**: Service tương tác với Firebase Realtime Database
  - CRUD operations cho notifications
  - Quản lý trạng thái user notifications
  - Stream updates realtime

### UI Components
- **NotificationPage**: Trang chính hiển thị danh sách thông báo
- **RealtimeNotificationManagementPage**: Trang admin quản lý thông báo
- **NotificationSampleDataPage**: Trang tạo dữ liệu mẫu

## Cách sử dụng

### Cho người dùng thông thường
1. Truy cập thông báo từ home page (icon notification)
2. Xem danh sách thông báo với bộ lọc
3. Tap vào thông báo để xem chi tiết
4. Đánh dấu đã đọc/xóa thông báo

### Cho Admin
1. Truy cập Admin Dashboard
2. Chọn "Notification Management" để tạo thông báo mới
3. Chọn "Sample Data" để tạo dữ liệu mẫu test
4. Quản lý và theo dõi thống kê thông báo

### Cho Developer
1. Có thể tạo thông báo với loại "bug_report"
2. Nhận thông báo về deployment và system issues
3. Truy cập đầy đủ tính năng admin

## API Reference

### RealtimeNotificationController

#### Phương thức chính
```dart
// Lấy danh sách thông báo
List<NotificationViewModel> get notifications

// Lấy thông báo đã lọc
List<NotificationViewModel> get filteredNotifications

// Đánh dấu đã đọc
Future<bool> markAsRead(String notificationId)

// Đánh dấu tất cả đã đọc
Future<bool> markAllAsRead()

// Xóa thông báo
Future<bool> deleteNotification(String notificationId)

// Tạo thông báo mới
Future<String?> createNotification({...})

// Thay đổi bộ lọc
void setFilter(String filter)
```

#### Observable properties
```dart
// Danh sách thông báo
RxList<NotificationViewModel> notifications

// Số lượng chưa đọc
RxInt unreadCount

// Số lượng chưa xem
RxInt unseenCount

// Trạng thái loading
RxBool isLoading

// Bộ lọc hiện tại
RxString selectedFilter
```

### RealtimeDatabaseService

#### Phương thức chính
```dart
// Tạo thông báo
Future<String?> createNotification({...})

// Lấy thông báo công khai
Stream<List<RealtimeNotificationModel>> getPublicNotificationsStream()

// Lấy thông báo của user
Stream<List<RealtimeNotificationModel>> getUserNotificationsStream(String userId)

// Đánh dấu đã đọc
Future<bool> markNotificationAsRead(String userId, String notificationId)

// Đánh dấu đã xem
Future<bool> markNotificationAsSeen(String userId, String notificationId)

// Xóa thông báo
Future<bool> deleteNotification(String userId, String notificationId)
```

## Database Structure

### Firebase Realtime Database
```
notifications/
  ├── {notificationId}/
  │   ├── title: string
  │   ├── body: string
  │   ├── imageUrl: string?
  │   ├── createdAt: number
  │   ├── expiresAt: number
  │   ├── type: string
  │   ├── priority: string
  │   ├── isPublic: boolean
  │   ├── targetUserIds: array?
  │   ├── targetScreen: string?
  │   └── data: object?

user_notifications/
  ├── {userNotificationId}/
  │   ├── userId: string
  │   ├── notificationId: string
  │   ├── isRead: boolean
  │   ├── isSeen: boolean
  │   ├── isDeleted: boolean
  │   ├── createdAt: number
  │   ├── readAt: number?
  │   ├── seenAt: number?
  │   └── deletedAt: number?
```

## Migration từ hệ thống cũ

### Files đã xóa
- `lib/services/notification_service.dart`
- `lib/controllers/notification_controller.dart`
- `lib/models/notification_model.dart`
- `lib/view/page/new_notification_page.dart`
- `lib/view/page/notification_tabs_page.dart`
- `lib/view/admin/notification_management_page.dart`

### Files mới
- `lib/view/page/notification_page.dart` (thay thế hoàn toàn)
- `lib/view/admin/realtime_notification_management_page.dart`
- `lib/view/admin/notification_sample_data_page.dart`
- `lib/utils/sample_notification_data.dart`

### Navigation updates
- Route `/notifications` -> `NotificationPage`
- Route `/admin/notifications` -> `RealtimeNotificationManagementPage`
- Route `/admin/notification_sample_data` -> `NotificationSampleDataPage`

## Testing

### Tạo dữ liệu mẫu
1. Truy cập Admin Dashboard
2. Chọn "Sample Data"
3. Chọn loại dữ liệu muốn tạo:
   - Public notifications
   - Admin notifications
   - Developer notifications
   - All sample data

### Test cases
- [x] Hiển thị danh sách thông báo realtime
- [x] Bộ lọc theo loại và trạng thái
- [x] Đánh dấu đã đọc/chưa đọc
- [x] Xóa thông báo
- [x] Tạo thông báo mới (admin)
- [x] Thống kê số lượng thông báo
- [x] Navigation đến target screen
- [x] Hiển thị chi tiết thông báo

## Performance

### Optimizations
- **Stream-based updates**: Chỉ cập nhật khi có thay đổi
- **Local state management**: Cache dữ liệu để giảm network calls
- **Lazy loading**: Load thông báo theo batch
- **Efficient filtering**: Filter trên client side

### Monitoring
- Theo dõi số lượng thông báo active
- Monitor performance của Realtime Database
- Track user engagement với notifications

## Security

### Database Rules
```javascript
{
  "rules": {
    "notifications": {
      ".read": "auth != null",
      ".write": "auth != null && (auth.token.admin == true || auth.token.developer == true)"
    },
    "user_notifications": {
      "$uid": {
        ".read": "auth != null && auth.uid == $uid",
        ".write": "auth != null && auth.uid == $uid"
      }
    }
  }
}
```

### Access Control
- **Public notifications**: Tất cả user đã đăng nhập
- **Private notifications**: Chỉ target users
- **Admin notifications**: Chỉ admin và developer
- **Create permissions**: Chỉ admin và developer

## Future Enhancements

### Planned Features
- [ ] Push notifications integration
- [ ] Notification scheduling
- [ ] Rich media support (video, audio)
- [ ] Notification templates
- [ ] Analytics và reporting
- [ ] Bulk operations
- [ ] Notification categories management
- [ ] User notification preferences

### Technical Improvements
- [ ] Offline support
- [ ] Background sync
- [ ] Image caching
- [ ] Performance monitoring
- [ ] Error tracking
- [ ] A/B testing support

## Troubleshooting

### Common Issues
1. **Thông báo không hiển thị realtime**
   - Kiểm tra Firebase Realtime Database connection
   - Verify database rules
   - Check user authentication

2. **Trạng thái đọc/chưa đọc không cập nhật**
   - Kiểm tra user_notifications collection
   - Verify userId mapping
   - Check network connectivity

3. **Performance issues**
   - Monitor số lượng active listeners
   - Optimize database queries
   - Check for memory leaks

### Debug Tools
- Firebase Console để monitor database
- Flutter Inspector để check widget tree
- Network tab để monitor API calls
- GetX DevTools để check state management

---

**Hệ thống thông báo realtime mới đã sẵn sàng sử dụng với đầy đủ tính năng và hiệu suất cao!**
