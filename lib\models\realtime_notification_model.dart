class RealtimeNotificationModel {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final int createdAt;
  final int expiresAt;
  final String? targetScreen;
  final Map<String, dynamic>? data;
  final bool isPublic;
  final List<String>? targetUserIds;
  final String type; // 'system', 'movie', 'promo', 'ticket', 'bug_report'
  final String priority; // 'low', 'normal', 'high', 'urgent'

  RealtimeNotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    required this.createdAt,
    required this.expiresAt,
    this.targetScreen,
    this.data,
    required this.isPublic,
    this.targetUserIds,
    this.type = 'system',
    this.priority = 'normal',
  });

  factory RealtimeNotificationModel.fromJson(
      String id, Map<String, dynamic> json) {
    return RealtimeNotificationModel(
      id: id,
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      imageUrl: json['imageUrl'],
      createdAt: json['createdAt'] ?? 0,
      expiresAt: json['expiresAt'] ?? 0,
      targetScreen: json['targetScreen'],
      data:
          json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
      isPublic: json['isPublic'] ?? true,
      targetUserIds: json['targetUserIds'] != null
          ? List<String>.from(json['targetUserIds'])
          : null,
      type: json['type'] ?? 'system',
      priority: json['priority'] ?? 'normal',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'createdAt': createdAt,
      'expiresAt': expiresAt,
      'targetScreen': targetScreen,
      'data': data,
      'isPublic': isPublic,
      'targetUserIds': targetUserIds,
      'type': type,
      'priority': priority,
    };
  }

  // Helper methods
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt);
  DateTime get expiresAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(expiresAt);

  bool get isExpired => DateTime.now().millisecondsSinceEpoch > expiresAt;

  RealtimeNotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? imageUrl,
    int? createdAt,
    int? expiresAt,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool? isPublic,
    List<String>? targetUserIds,
    String? type,
    String? priority,
  }) {
    return RealtimeNotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      targetScreen: targetScreen ?? this.targetScreen,
      data: data ?? this.data,
      isPublic: isPublic ?? this.isPublic,
      targetUserIds: targetUserIds ?? this.targetUserIds,
      type: type ?? this.type,
      priority: priority ?? this.priority,
    );
  }
}

class RealtimeUserNotificationModel {
  final String id;
  final String userId;
  final String notificationId;
  final bool isRead;
  final bool isSeen;
  final bool isDeleted;
  final int createdAt;
  final int? readAt;
  final int? seenAt;
  final int? deletedAt;

  RealtimeUserNotificationModel({
    required this.id,
    required this.userId,
    required this.notificationId,
    required this.isRead,
    required this.isSeen,
    required this.isDeleted,
    required this.createdAt,
    this.readAt,
    this.seenAt,
    this.deletedAt,
  });

  factory RealtimeUserNotificationModel.fromJson(
      String id, Map<String, dynamic> json) {
    return RealtimeUserNotificationModel(
      id: id,
      userId: json['userId'] ?? '',
      notificationId: json['notificationId'] ?? '',
      isRead: json['isRead'] ?? false,
      isSeen: json['isSeen'] ?? false,
      isDeleted: json['isDeleted'] ?? false,
      createdAt: json['createdAt'] ?? 0,
      readAt: json['readAt'],
      seenAt: json['seenAt'],
      deletedAt: json['deletedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'notificationId': notificationId,
      'isRead': isRead,
      'isSeen': isSeen,
      'isDeleted': isDeleted,
      'createdAt': createdAt,
      'readAt': readAt,
      'seenAt': seenAt,
      'deletedAt': deletedAt,
    };
  }

  // Helper methods
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt);
  DateTime? get readAtDateTime =>
      readAt != null ? DateTime.fromMillisecondsSinceEpoch(readAt!) : null;
  DateTime? get seenAtDateTime =>
      seenAt != null ? DateTime.fromMillisecondsSinceEpoch(seenAt!) : null;
  DateTime? get deletedAtDateTime => deletedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(deletedAt!)
      : null;

  RealtimeUserNotificationModel copyWith({
    String? id,
    String? userId,
    String? notificationId,
    bool? isRead,
    bool? isSeen,
    bool? isDeleted,
    int? createdAt,
    int? readAt,
    int? seenAt,
    int? deletedAt,
  }) {
    return RealtimeUserNotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      notificationId: notificationId ?? this.notificationId,
      isRead: isRead ?? this.isRead,
      isSeen: isSeen ?? this.isSeen,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      seenAt: seenAt ?? this.seenAt,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }
}

// Combined model for UI display
class NotificationViewModel {
  final RealtimeNotificationModel notification;
  final RealtimeUserNotificationModel? userNotification;

  NotificationViewModel({
    required this.notification,
    this.userNotification,
  });

  bool get isRead => userNotification?.isRead ?? false;
  bool get isSeen => userNotification?.isSeen ?? false;
  bool get isDeleted => userNotification?.isDeleted ?? false;

  DateTime? get readAt => userNotification?.readAtDateTime;
  DateTime? get seenAt => userNotification?.seenAtDateTime;
  DateTime? get deletedAt => userNotification?.deletedAtDateTime;
}
