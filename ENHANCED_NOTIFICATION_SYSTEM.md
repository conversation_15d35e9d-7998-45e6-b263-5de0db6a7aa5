# Hệ thống thông báo nâng cao - Đớp Phim

## Tổng quan

Hệ thống thông báo mới của ứng dụng Đớp Phim được xây dựng với các tính năng nâng cao:

- **Real-time notifications** với Firebase Realtime Database
- **Notification settings** cho phép người dùng tùy chỉnh
- **Scheduled notifications** với khả năng lên lịch
- **Bulk operations** cho thao tác hàng loạt
- **Statistics và analytics** cho thông báo
- **Enhanced UI/UX** với search, filter, và animations

## Kiến trúc hệ thống

### 1. Models
- `RealtimeNotificationModel`: Model chính cho thông báo
- `RealtimeUserNotificationModel`: Model trạng thái thông báo của user
- `NotificationViewModel`: Model kết hợp để hiển thị UI
- `NotificationSettingsModel`: Model cài đặt thông báo
- `NotificationStatsModel`: Model thống kê thông báo

### 2. Services
- `RealtimeDatabaseService`: Service tương tác với Firebase Realtime Database
- `NotificationSchedulerService`: Service xử lý scheduled notifications và cleanup

### 3. Controllers
- `RealtimeNotificationController`: Controller chính quản lý thông báo
- `NotificationSettingsController`: Controller quản lý cài đặt thông báo

### 4. UI Components
- `NotificationPage`: Trang hiển thị danh sách thông báo
- `NotificationSettingsPage`: Trang cài đặt thông báo

## Tính năng chính

### 1. Real-time Notifications
- Thông báo được cập nhật real-time qua Firebase Realtime Database
- Hỗ trợ thông báo công khai và cá nhân
- Phân loại theo type: system, movie, promo, ticket, bug_report
- Độ ưu tiên: low, normal, high, urgent

### 2. Notification Settings
- Bật/tắt từng loại thông báo
- Cài đặt giờ yên lặng (quiet hours)
- Giới hạn số thông báo tối đa mỗi ngày
- Nhóm thông báo tương tự
- Cài đặt âm thanh và rung

### 3. Scheduled Notifications
- Lên lịch thông báo để gửi sau
- Tự động kích hoạt khi đến thời gian
- Hỗ trợ expiration time

### 4. Enhanced UI Features
- Search thông báo theo title/body
- Filter theo loại, trạng thái, độ ưu tiên
- Bulk operations: mark all read, delete multiple
- Statistics và analytics
- Improved notification details view

### 5. Automatic Cleanup
- Tự động xóa thông báo hết hạn
- Reset daily notification count
- Cleanup expired user notifications

## Cấu trúc Database

### Firebase Realtime Database
```
notifications/
  ├── {notificationId}/
  │   ├── title: string
  │   ├── body: string
  │   ├── imageUrl: string?
  │   ├── createdAt: number
  │   ├── expiresAt: number
  │   ├── type: string
  │   ├── priority: string
  │   ├── isPublic: boolean
  │   ├── targetUserIds: array?
  │   ├── targetScreen: string?
  │   ├── data: object?
  │   ├── isScheduled: boolean
  │   ├── scheduledAt: number?
  │   ├── tags: array?
  │   ├── actionButtons: object?
  │   └── status: string

user_notifications/
  ├── {userNotificationId}/
  │   ├── userId: string
  │   ├── notificationId: string
  │   ├── isRead: boolean
  │   ├── isSeen: boolean
  │   ├── isDeleted: boolean
  │   ├── createdAt: number
  │   ├── readAt: number?
  │   ├── seenAt: number?
  │   └── deletedAt: number?

notification_settings/
  ├── {userId}/
  │   ├── enablePushNotifications: boolean
  │   ├── enableSystemNotifications: boolean
  │   ├── enableMovieNotifications: boolean
  │   ├── enablePromoNotifications: boolean
  │   ├── enableTicketNotifications: boolean
  │   ├── enableBugReportNotifications: boolean
  │   ├── enableSound: boolean
  │   ├── enableVibration: boolean
  │   ├── quietHoursStart: string
  │   ├── quietHoursEnd: string
  │   ├── enableQuietHours: boolean
  │   ├── mutedTypes: array
  │   ├── maxNotificationsPerDay: number
  │   ├── groupSimilarNotifications: boolean
  │   ├── createdAt: number
  │   └── updatedAt: number

notification_stats/
  ├── {dateKey}/
  │   └── {type}/
  │       ├── created: number
  │       ├── read: number
  │       └── deleted: number
```

## Cách sử dụng

### 1. Khởi tạo hệ thống
```dart
// Trong main.dart hoặc app initialization
Get.put(RealtimeDatabaseService(), permanent: true);
Get.put(NotificationSchedulerService(), permanent: true);
```

### 2. Tạo thông báo cơ bản
```dart
final controller = Get.find<RealtimeNotificationController>();

await controller.createNotification(
  title: 'Thông báo mới',
  body: 'Nội dung thông báo',
  type: 'system',
  priority: 'normal',
  isPublic: true,
);
```

### 3. Tạo thông báo nâng cao
```dart
await controller.createEnhancedNotification(
  title: 'Thông báo được lên lịch',
  body: 'Sẽ được gửi sau 1 giờ',
  type: 'movie',
  priority: 'high',
  scheduledAt: DateTime.now().add(Duration(hours: 1)),
  tags: ['promotion', 'movie'],
  actionButtons: {
    'view': {'label': 'Xem ngay', 'action': 'view_movie'},
    'dismiss': {'label': 'Bỏ qua', 'action': 'dismiss'},
  },
);
```

### 4. Quản lý cài đặt thông báo
```dart
final settingsController = Get.find<NotificationSettingsController>();

// Cập nhật cài đặt
settingsController.updateSetting('enableMovieNotifications', false);
settingsController.updateSetting('quietHoursStart', '22:00');
settingsController.updateSetting('quietHoursEnd', '08:00');

// Lưu cài đặt
await settingsController.saveSettings();
```

### 5. Thao tác hàng loạt
```dart
// Đánh dấu nhiều thông báo đã đọc
final notificationIds = ['id1', 'id2', 'id3'];
await controller.markMultipleAsRead(notificationIds);

// Xóa nhiều thông báo
await controller.deleteMultipleNotifications(notificationIds);
```

## Navigation

### Routes được thêm mới:
- `/notification_settings`: Trang cài đặt thông báo

### Cách điều hướng:
```dart
// Đến trang cài đặt thông báo
Get.toNamed('/notification_settings');
```

## Performance Optimizations

1. **Lazy Loading**: Controllers được khởi tạo khi cần thiết
2. **Stream Management**: Tự động hủy streams khi không sử dụng
3. **Indexing**: Database được index cho các truy vấn thường dùng
4. **Cleanup**: Tự động dọn dẹp dữ liệu cũ và hết hạn

## Security

1. **Database Rules**: Chỉ user được phép đọc/ghi notification settings của mình
2. **Authentication**: Tất cả operations đều yêu cầu authentication
3. **Validation**: Input được validate trước khi lưu vào database

## Monitoring và Debugging

1. **Scheduler Status**: Kiểm tra trạng thái của schedulers
2. **Statistics**: Theo dõi số lượng thông báo được tạo/đọc/xóa
3. **Error Handling**: Log errors và hiển thị thông báo lỗi cho user

## Tương lai

Các tính năng có thể được thêm vào:
1. Push notifications với FCM
2. Rich notifications với images và videos
3. Notification templates
4. A/B testing cho notifications
5. Machine learning để tối ưu thời gian gửi
6. Integration với external services
