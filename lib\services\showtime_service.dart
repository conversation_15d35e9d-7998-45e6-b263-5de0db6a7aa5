import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/showtime_model.dart';

class ShowtimeService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'showtimes';

  // Get all showtimes
  Future<List<ShowtimeModel>> getAllShowtimes() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes: $e');
    }
  }

  // Get showtimes by movie
  Future<List<ShowtimeModel>> getShowtimesByMovie(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('status', isEqualTo: 'active')
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by movie: $e');
    }
  }

  // Get showtimes by theater
  Future<List<ShowtimeModel>> getShowtimesByTheater(String theaterId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('theaterId', isEqualTo: theaterId)
          .where('status', isEqualTo: 'active')
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by theater: $e');
    }
  }

  // Get showtimes by date
  Future<List<ShowtimeModel>> getShowtimesByDate(String date) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('date', isEqualTo: date)
          .where('status', isEqualTo: 'active')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by date: $e');
    }
  }

  // Get showtimes by movie and date
  Future<List<ShowtimeModel>> getShowtimesByMovieAndDate(
      int movieId, String date) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('date', isEqualTo: date)
          .where('status', isEqualTo: 'active')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by movie and date: $e');
    }
  }

  // Get showtimes by movie and theater
  Future<List<ShowtimeModel>> getShowtimesByMovieAndTheater(
      int movieId, String theaterId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('theaterId', isEqualTo: theaterId)
          .where('status', isEqualTo: 'active')
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by movie and theater: $e');
    }
  }

  // Get showtimes by movie, theater and date
  Future<List<ShowtimeModel>> getShowtimesByMovieTheaterAndDate(
      int movieId, String theaterId, String date) async {
    try {
      print(
          'ShowtimeService.getShowtimesByMovieTheaterAndDate: movieId=$movieId, theaterId=$theaterId, date=$date');

      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('theaterId', isEqualTo: theaterId)
          .where('date', isEqualTo: date)
          .where('status', isEqualTo: 'active')
          .orderBy('time')
          .get();

      print('Found ${snapshot.docs.length} documents in Firestore');

      final showtimes =
          snapshot.docs.map((doc) => ShowtimeModel.fromFirestore(doc)).toList();

      print('Parsed ${showtimes.length} showtime models');
      for (var showtime in showtimes) {
        print('Showtime: ${showtime.time} on ${showtime.date}');
      }

      return showtimes;
    } catch (e) {
      print('Error in getShowtimesByMovieTheaterAndDate: $e');
      throw Exception('Failed to get showtimes by movie, theater and date: $e');
    }
  }

  // Get showtime by ID
  Future<ShowtimeModel?> getShowtimeById(String showtimeId) async {
    try {
      final doc =
          await _firestore.collection(_collection).doc(showtimeId).get();

      if (doc.exists) {
        return ShowtimeModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get showtime: $e');
    }
  }

  // Get available showtimes (not full, not cancelled, not ended)
  Future<List<ShowtimeModel>> getAvailableShowtimes() async {
    try {
      final now = DateTime.now();
      final today =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      final snapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: 'active')
          .where('date', isGreaterThanOrEqualTo: today)
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .where((showtime) => showtime.isBookable)
          .toList();
    } catch (e) {
      throw Exception('Failed to get available showtimes: $e');
    }
  }

  // Book seats with transaction to prevent race conditions
  Future<void> bookSeats(String showtimeId, List<String> seatIds) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final showtimeRef = _firestore.collection(_collection).doc(showtimeId);
        final showtimeDoc = await transaction.get(showtimeRef);

        if (!showtimeDoc.exists) {
          throw Exception('Showtime not found');
        }

        final showtimeData = showtimeDoc.data()!;
        final currentBookedSeats =
            List<String>.from(showtimeData['bookedSeats'] ?? []);
        final currentReservedSeats =
            List<String>.from(showtimeData['reservedSeats'] ?? []);
        final currentAvailableSeats = showtimeData['availableSeats'] as int;

        // Check if any of the requested seats are already booked
        for (String seatId in seatIds) {
          if (currentBookedSeats.contains(seatId)) {
            throw Exception('Ghế $seatId đã được đặt bởi người khác');
          }
        }

        // Remove seats from reserved list and add to booked list
        final updatedReservedSeats = currentReservedSeats
            .where((seat) => !seatIds.contains(seat))
            .toList();
        final updatedBookedSeats = [...currentBookedSeats, ...seatIds];
        final updatedAvailableSeats = currentAvailableSeats - seatIds.length;

        // Update the document
        transaction.update(showtimeRef, {
          'bookedSeats': updatedBookedSeats,
          'reservedSeats': updatedReservedSeats,
          'availableSeats': updatedAvailableSeats,
          'updatedAt': Timestamp.now(),
        });
      });
    } catch (e) {
      if (e.toString().contains('đã được đặt')) {
        rethrow; // Re-throw seat conflict errors as-is
      }
      throw Exception('Không thể đặt ghế: $e');
    }
  }

  // Reserve seats temporarily with transaction and timeout
  Future<void> reserveSeats(String showtimeId, List<String> seatIds,
      {String? userId}) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final showtimeRef = _firestore.collection(_collection).doc(showtimeId);
        final showtimeDoc = await transaction.get(showtimeRef);

        if (!showtimeDoc.exists) {
          throw Exception('Showtime not found');
        }

        final showtimeData = showtimeDoc.data()!;
        final currentBookedSeats =
            List<String>.from(showtimeData['bookedSeats'] ?? []);
        final currentReservedSeats =
            List<String>.from(showtimeData['reservedSeats'] ?? []);
        final reservationData =
            Map<String, dynamic>.from(showtimeData['reservationData'] ?? {});

        // Check if any of the requested seats are already booked or reserved
        for (String seatId in seatIds) {
          if (currentBookedSeats.contains(seatId)) {
            throw Exception('Ghế $seatId đã được đặt');
          }
          if (currentReservedSeats.contains(seatId)) {
            // Check if reservation is still valid (not expired)
            final reservationInfo = reservationData[seatId];
            if (reservationInfo != null) {
              final reservedAt =
                  (reservationInfo['reservedAt'] as Timestamp).toDate();
              final reservedBy = reservationInfo['userId'] as String?;
              final now = DateTime.now();

              // If reservation is still valid and by different user (5 minutes timeout)
              if (now.difference(reservedAt).inMinutes < 5 &&
                  reservedBy != userId) {
                throw Exception('Ghế $seatId đang được giữ bởi người khác');
              }
            }
          }
        }

        // Clean up expired reservations first
        final now = DateTime.now();
        final cleanedReservedSeats = <String>[];
        final cleanedReservationData = <String, dynamic>{};

        for (String seat in currentReservedSeats) {
          final reservationInfo = reservationData[seat];
          if (reservationInfo != null) {
            final reservedAt =
                (reservationInfo['reservedAt'] as Timestamp).toDate();
            if (now.difference(reservedAt).inMinutes < 5) {
              cleanedReservedSeats.add(seat);
              cleanedReservationData[seat] = reservationInfo;
            }
          }
        }

        // Add new reservations
        final updatedReservedSeats = [...cleanedReservedSeats, ...seatIds];
        final updatedReservationData =
            Map<String, dynamic>.from(cleanedReservationData);

        for (String seatId in seatIds) {
          updatedReservationData[seatId] = {
            'userId': userId ?? 'anonymous',
            'reservedAt': Timestamp.now(),
          };
        }

        // Update the document
        transaction.update(showtimeRef, {
          'reservedSeats': updatedReservedSeats,
          'reservationData': updatedReservationData,
          'updatedAt': Timestamp.now(),
        });
      });
    } catch (e) {
      if (e.toString().contains('đã được đặt') ||
          e.toString().contains('đang được giữ')) {
        rethrow; // Re-throw seat conflict errors as-is
      }
      throw Exception('Không thể giữ ghế: $e');
    }
  }

  // Release reserved seats with transaction
  Future<void> releaseSeats(String showtimeId, List<String> seatIds,
      {String? userId}) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final showtimeRef = _firestore.collection(_collection).doc(showtimeId);
        final showtimeDoc = await transaction.get(showtimeRef);

        if (!showtimeDoc.exists) {
          throw Exception('Showtime not found');
        }

        final showtimeData = showtimeDoc.data()!;
        final currentReservedSeats =
            List<String>.from(showtimeData['reservedSeats'] ?? []);
        final reservationData =
            Map<String, dynamic>.from(showtimeData['reservationData'] ?? {});

        // Remove seats from reserved list and reservation data
        final updatedReservedSeats = currentReservedSeats
            .where((seat) => !seatIds.contains(seat))
            .toList();

        final updatedReservationData =
            Map<String, dynamic>.from(reservationData);
        for (String seatId in seatIds) {
          // Only remove if reserved by the same user or if userId is null (admin action)
          if (userId == null ||
              (reservationData[seatId] != null &&
                  reservationData[seatId]['userId'] == userId)) {
            updatedReservationData.remove(seatId);
          }
        }

        // Update the document
        transaction.update(showtimeRef, {
          'reservedSeats': updatedReservedSeats,
          'reservationData': updatedReservationData,
          'updatedAt': Timestamp.now(),
        });
      });
    } catch (e) {
      throw Exception('Không thể hủy giữ ghế: $e');
    }
  }

  // Release reserved seats (alias for backward compatibility)
  Future<void> releaseReservedSeats(
      String showtimeId, List<String> seatIds) async {
    return releaseSeats(showtimeId, seatIds);
  }

  // Release booked seats when ticket is cancelled
  Future<void> releaseBookedSeats(
      String showtimeId, List<String> seatIds) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final showtimeRef = _firestore.collection(_collection).doc(showtimeId);
        final showtimeDoc = await transaction.get(showtimeRef);

        if (!showtimeDoc.exists) {
          throw Exception('Showtime not found');
        }

        final showtimeData = showtimeDoc.data()!;
        final currentBookedSeats =
            List<String>.from(showtimeData['bookedSeats'] ?? []);
        final currentAvailableSeats = showtimeData['availableSeats'] as int;

        // Only release seats that are actually booked
        final seatsToRelease = seatIds
            .where((seatId) => currentBookedSeats.contains(seatId))
            .toList();

        if (seatsToRelease.isEmpty) {
          print(
              'No seats to release from showtime $showtimeId - seats $seatIds not found in booked list');
          return; // Nothing to release
        }

        // Remove seats from booked list
        final updatedBookedSeats = currentBookedSeats
            .where((seat) => !seatsToRelease.contains(seat))
            .toList();

        // Get screen capacity to validate
        final screenId = showtimeData['screenId'] as String;
        final screenDoc =
            await _firestore.collection('screens').doc(screenId).get();
        final screenCapacity = screenDoc.exists
            ? (screenDoc.data()?['totalSeats'] as int? ?? 60)
            : 60;

        // Calculate new available seats, but don't exceed screen capacity
        final newAvailableSeats = currentAvailableSeats + seatsToRelease.length;
        final updatedAvailableSeats = newAvailableSeats > screenCapacity
            ? screenCapacity
            : newAvailableSeats;

        print(
            'Releasing booked seats: $seatsToRelease from showtime $showtimeId');
        print('Screen capacity: $screenCapacity');
        print(
            'Before: ${currentBookedSeats.length} booked, $currentAvailableSeats available');
        print(
            'After: ${updatedBookedSeats.length} booked, $updatedAvailableSeats available');

        // Validate data consistency
        final totalOccupied = updatedBookedSeats.length;
        final expectedAvailable = screenCapacity - totalOccupied;

        if (updatedAvailableSeats != expectedAvailable) {
          print(
              '⚠️ Data inconsistency detected! Fixing availableSeats from $updatedAvailableSeats to $expectedAvailable');
        }

        // Update the document with corrected values
        transaction.update(showtimeRef, {
          'bookedSeats': updatedBookedSeats,
          'availableSeats':
              expectedAvailable, // Use calculated value for consistency
          'updatedAt': Timestamp.now(),
        });
      });
    } catch (e) {
      print('Error releasing booked seats: $e');
      throw Exception('Không thể giải phóng ghế đã đặt: $e');
    }
  }

  // Fix data inconsistency for all showtimes
  Future<void> fixAllShowtimeDataConsistency() async {
    try {
      print('Starting showtime data consistency fix...');

      final showtimesSnapshot = await _firestore.collection(_collection).get();
      int fixedCount = 0;
      int errorCount = 0;

      for (final doc in showtimesSnapshot.docs) {
        try {
          final showtimeData = doc.data();
          final showtimeId = doc.id;
          final screenId = showtimeData['screenId'] as String;
          final currentBookedSeats =
              List<String>.from(showtimeData['bookedSeats'] ?? []);
          final currentAvailableSeats = showtimeData['availableSeats'] as int;

          // Get screen capacity
          final screenDoc =
              await _firestore.collection('screens').doc(screenId).get();
          final screenCapacity = screenDoc.exists
              ? (screenDoc.data()?['totalSeats'] as int? ?? 60)
              : 60;

          // Calculate expected available seats
          final expectedAvailable = screenCapacity - currentBookedSeats.length;

          if (currentAvailableSeats != expectedAvailable) {
            print(
                'Fixing showtime $showtimeId: $currentAvailableSeats -> $expectedAvailable (capacity: $screenCapacity, booked: ${currentBookedSeats.length})');

            await _firestore.collection(_collection).doc(showtimeId).update({
              'availableSeats': expectedAvailable,
              'updatedAt': Timestamp.now(),
            });

            fixedCount++;
          }
        } catch (e) {
          print('Error fixing showtime ${doc.id}: $e');
          errorCount++;
        }
      }

      print(
          'Data consistency fix completed: $fixedCount fixed, $errorCount errors');
    } catch (e) {
      print('Error during data consistency fix: $e');
      throw Exception('Failed to fix showtime data consistency: $e');
    }
  }

  // Sync tickets and seats data
  Future<Map<String, int>> syncTicketsAndSeats() async {
    try {
      print('Starting tickets and seats synchronization...');

      int syncedTickets = 0;
      int issuesFound = 0;

      // Get all tickets from Firestore
      final ticketsSnapshot = await _firestore.collection('tickets').get();

      // Group tickets by showtime
      final Map<String, List<Map<String, dynamic>>> ticketsByShowtime = {};

      for (final ticketDoc in ticketsSnapshot.docs) {
        final ticketData = ticketDoc.data();
        final showtimeId =
            ticketData['showtimeId'] ?? ticketData['showtime_id'] ?? '';
        final status = ticketData['status'] ?? 'confirmed';

        // Only process confirmed tickets
        if (showtimeId.isNotEmpty && status == 'confirmed') {
          if (!ticketsByShowtime.containsKey(showtimeId)) {
            ticketsByShowtime[showtimeId] = [];
          }
          ticketsByShowtime[showtimeId]!.add({
            'id': ticketDoc.id,
            'data': ticketData,
          });
        }
      }

      print('Found ${ticketsByShowtime.length} showtimes with tickets');

      // Process each showtime
      for (final entry in ticketsByShowtime.entries) {
        final showtimeId = entry.key;
        final tickets = entry.value;

        try {
          // Get current showtime data
          final showtimeDoc =
              await _firestore.collection(_collection).doc(showtimeId).get();

          if (!showtimeDoc.exists) {
            print('Showtime $showtimeId not found, skipping...');
            continue;
          }

          final showtimeData = showtimeDoc.data()!;
          final currentBookedSeats =
              List<String>.from(showtimeData['bookedSeats'] ?? []);

          // Collect all seats from tickets
          final Set<String> seatsFromTickets = {};

          for (final ticket in tickets) {
            final ticketData = ticket['data'];
            final seats = ticketData['seats'] as List<dynamic>? ?? [];

            for (final seat in seats) {
              String seatId = '';
              if (seat is Map<String, dynamic>) {
                final row = seat['row'] ?? '';
                final number = seat['number'] ?? '';
                seatId = '$row$number';
              } else if (seat is String) {
                seatId = seat;
              }

              if (seatId.isNotEmpty) {
                seatsFromTickets.add(seatId);
              }
            }
          }

          // Check for missing seats in showtime
          final missingSeats = seatsFromTickets
              .where((seat) => !currentBookedSeats.contains(seat))
              .toList();

          if (missingSeats.isNotEmpty) {
            print('Showtime $showtimeId: Adding missing seats: $missingSeats');

            // Add missing seats to booked seats
            final updatedBookedSeats = [...currentBookedSeats, ...missingSeats];

            // Get screen capacity for validation
            final screenId = showtimeData['screenId'] as String;
            final screenDoc =
                await _firestore.collection('screens').doc(screenId).get();
            final screenCapacity = screenDoc.exists
                ? (screenDoc.data()?['totalSeats'] as int? ?? 100)
                : 100;

            // Calculate correct available seats
            final correctAvailableSeats =
                screenCapacity - updatedBookedSeats.length;

            // Update showtime
            await _firestore.collection(_collection).doc(showtimeId).update({
              'bookedSeats': updatedBookedSeats,
              'availableSeats': correctAvailableSeats,
              'updatedAt': Timestamp.now(),
            });

            issuesFound++;
          }

          syncedTickets += tickets.length;
        } catch (e) {
          print('Error processing showtime $showtimeId: $e');
        }
      }

      print(
          'Tickets and seats sync completed: $syncedTickets tickets processed, $issuesFound issues fixed');

      return {
        'synced': syncedTickets,
        'issues': issuesFound,
      };
    } catch (e) {
      print('Error during tickets and seats sync: $e');
      throw Exception('Failed to sync tickets and seats: $e');
    }
  }

  // Clean up expired reservations
  Future<void> cleanupExpiredReservations(String showtimeId) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final showtimeRef = _firestore.collection(_collection).doc(showtimeId);
        final showtimeDoc = await transaction.get(showtimeRef);

        if (!showtimeDoc.exists) {
          return;
        }

        final showtimeData = showtimeDoc.data()!;
        final currentReservedSeats =
            List<String>.from(showtimeData['reservedSeats'] ?? []);
        final reservationData =
            Map<String, dynamic>.from(showtimeData['reservationData'] ?? {});

        final now = DateTime.now();
        final validReservedSeats = <String>[];
        final validReservationData = <String, dynamic>{};

        for (String seat in currentReservedSeats) {
          final reservationInfo = reservationData[seat];
          if (reservationInfo != null) {
            final reservedAt =
                (reservationInfo['reservedAt'] as Timestamp).toDate();
            if (now.difference(reservedAt).inMinutes < 5) {
              validReservedSeats.add(seat);
              validReservationData[seat] = reservationInfo;
            }
          }
        }

        // Only update if there are changes
        if (validReservedSeats.length != currentReservedSeats.length) {
          transaction.update(showtimeRef, {
            'reservedSeats': validReservedSeats,
            'reservationData': validReservationData,
            'updatedAt': Timestamp.now(),
          });
        }
      });
    } catch (e) {
      print('Error cleaning up expired reservations: $e');
    }
  }

  // Update available seats for a specific showtime
  Future<void> updateShowtimeAvailableSeats(
      String showtimeId, int availableSeats) async {
    try {
      await _firestore.collection(_collection).doc(showtimeId).update({
        'availableSeats': availableSeats,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update showtime available seats: $e');
    }
  }

  // Admin functions
  Future<ShowtimeModel> createShowtime(ShowtimeModel showtime) async {
    return await addShowtime(showtime);
  }

  Future<ShowtimeModel> addShowtime(ShowtimeModel showtime) async {
    try {
      final now = DateTime.now();
      final showtimeData = showtime.copyWith(
        createdAt: now,
        updatedAt: now,
      );

      final docRef = await _firestore
          .collection(_collection)
          .add(showtimeData.toFirestore());

      return showtimeData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add showtime: $e');
    }
  }

  Future<void> updateShowtime(ShowtimeModel showtime) async {
    try {
      final updatedShowtime = showtime.copyWith(updatedAt: DateTime.now());

      await _firestore
          .collection(_collection)
          .doc(showtime.id)
          .update(updatedShowtime.toFirestore());
    } catch (e) {
      throw Exception('Failed to update showtime: $e');
    }
  }

  Future<void> cancelShowtime(String showtimeId) async {
    try {
      await _firestore.collection(_collection).doc(showtimeId).update({
        'status': 'cancelled',
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to cancel showtime: $e');
    }
  }

  Future<void> deleteShowtime(String showtimeId) async {
    try {
      await _firestore.collection(_collection).doc(showtimeId).delete();
    } catch (e) {
      throw Exception('Failed to delete showtime: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<ShowtimeModel>> getShowtimesStream() {
    return _firestore
        .collection(_collection)
        .orderBy('date')
        .orderBy('time')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ShowtimeModel.fromFirestore(doc))
            .toList());
  }

  Stream<List<ShowtimeModel>> getShowtimesByMovieStream(int movieId) {
    return _firestore
        .collection(_collection)
        .where('movieId', isEqualTo: movieId)
        .where('status', isEqualTo: 'active')
        .orderBy('date')
        .orderBy('time')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ShowtimeModel.fromFirestore(doc))
            .toList());
  }

  Stream<ShowtimeModel?> getShowtimeStream(String showtimeId) {
    return _firestore
        .collection(_collection)
        .doc(showtimeId)
        .snapshots()
        .map((doc) => doc.exists ? ShowtimeModel.fromFirestore(doc) : null);
  }

  // Statistics
  Future<Map<String, dynamic>> getShowtimeStatistics() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();

      int totalShowtimes = 0;
      int activeShowtimes = 0;
      int cancelledShowtimes = 0;
      int fullShowtimes = 0;
      final movieCounts = <int, int>{};
      final theaterCounts = <String, int>{};
      final dateCounts = <String, int>{};

      for (final doc in snapshot.docs) {
        final showtime = ShowtimeModel.fromFirestore(doc);
        totalShowtimes++;

        switch (showtime.status) {
          case ShowtimeStatus.active:
            activeShowtimes++;
            break;
          case ShowtimeStatus.cancelled:
            cancelledShowtimes++;
            break;
          case ShowtimeStatus.full:
            fullShowtimes++;
            break;
          case ShowtimeStatus.ended:
            break;
        }

        // Count by movie
        movieCounts[showtime.movieId] =
            (movieCounts[showtime.movieId] ?? 0) + 1;

        // Count by theater
        theaterCounts[showtime.theaterId] =
            (theaterCounts[showtime.theaterId] ?? 0) + 1;

        // Count by date
        dateCounts[showtime.date] = (dateCounts[showtime.date] ?? 0) + 1;
      }

      return {
        'totalShowtimes': totalShowtimes,
        'activeShowtimes': activeShowtimes,
        'cancelledShowtimes': cancelledShowtimes,
        'fullShowtimes': fullShowtimes,
        'movieCounts': movieCounts,
        'theaterCounts': theaterCounts,
        'dateCounts': dateCounts,
      };
    } catch (e) {
      throw Exception('Failed to get showtime statistics: $e');
    }
  }
}
