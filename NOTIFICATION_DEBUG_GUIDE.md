# Hướng dẫn Debug vấn đề Duplicate Notifications

## Vấn đề đã được sửa

Tôi đã xác định và sửa vấn đề duplicate notifications. Vấn đề chính là:

### 1. **Nguyên nhân chính:**
- <PERSON><PERSON> đánh dấu thông báo đã đọc, nếu không tìm thấy `user_notification` record, hệ thống tạo mới một record
- <PERSON><PERSON><PERSON><PERSON> này gây ra việc có nhiều record cho cùng một thông báo
- Stream listener nhận được nhiều bản ghi và hiển thị duplicate

### 2. **C<PERSON>c thay đổi đã thực hiện:**

#### A. Cải thiện logic trong `markNotificationAsRead`:
```dart
// Trước đây: Luôn tạo record mới nếu không tìm thấy
// Bây giờ: Chỉ tạo record mới cho thông báo targeted, không tạo cho public notifications

if (shouldCreateRecord) {
  // Chỉ tạo khi thực sự cần thiết
  await _userNotificationsRef.push().set({...});
} else {
  // Thông báo public không cần tạo user record
  return true;
}
```

#### B. Thêm logging để debug:
```dart
print('markNotificationAsRead called: userId=$userId, notificationId=$notificationId');
print('Found ${data.length} user notification records for user $userId');
print('Found matching notification record: ${entry.key}');
```

#### C. Kiểm tra trạng thái trước khi cập nhật:
```dart
// Chỉ cập nhật nếu chưa đọc
if (!userNotification['isRead']) {
  print('Marking notification as read');
  await _userNotificationsRef.child(entry.key).update({...});
} else {
  print('Notification already marked as read');
}
```

## Cách kiểm tra và debug

### 1. **Kiểm tra Console Logs:**
Khi bạn đánh dấu thông báo đã đọc, hãy xem console để thấy:
```
markNotificationAsRead called: userId=abc123, notificationId=xyz789
Found 5 user notification records for user abc123
Found matching notification record: -N1234567890
Marking notification as read
```

### 2. **Kiểm tra Firebase Realtime Database:**
Vào Firebase Console > Realtime Database và kiểm tra:

```
user_notifications/
├── -N1234567890/
│   ├── userId: "abc123"
│   ├── notificationId: "xyz789"
│   ├── isRead: true
│   ├── isSeen: true
│   └── isDeleted: false
```

**Không nên có nhiều record với cùng `userId` và `notificationId`**

### 3. **Kiểm tra Stream Updates:**
Trong `RealtimeNotificationController`, stream sẽ tự động cập nhật khi database thay đổi:

```dart
// Controller không cập nhật local state
// Stream tự động emit update mới từ database
return success;
```

## Các bước test

### 1. **Test cơ bản:**
1. Mở app và xem danh sách thông báo
2. Đánh dấu một thông báo chưa đọc
3. Kiểm tra xem có duplicate không
4. Xem console logs

### 2. **Test edge cases:**
1. Đánh dấu cùng một thông báo nhiều lần
2. Đánh dấu thông báo public vs targeted
3. Test với nhiều user cùng lúc

### 3. **Test cleanup:**
1. Xóa thông báo và kiểm tra soft delete
2. Test mark all as read
3. Test refresh notifications

## Monitoring

### 1. **Database Rules đã được cập nhật:**
```json
{
  "user_notifications": {
    ".indexOn": ["userId", "notificationId", "isRead", "isSeen", "isDeleted", "createdAt"]
  }
}
```

### 2. **Performance Monitoring:**
- Stream listeners được quản lý tốt hơn
- Tránh multiple subscriptions
- Cleanup khi dispose

## Nếu vẫn có vấn đề

### 1. **Kiểm tra logs:**
```bash
flutter logs
```

### 2. **Clear app data:**
```bash
flutter clean
flutter pub get
```

### 3. **Kiểm tra Firebase Rules:**
Đảm bảo rules cho phép read/write user_notifications

### 4. **Manual cleanup database:**
Nếu có duplicate data trong database, có thể cần cleanup manual:

```javascript
// Firebase Console > Functions
// Script để xóa duplicate user_notifications
const admin = require('firebase-admin');
const db = admin.database();

async function cleanupDuplicates() {
  const snapshot = await db.ref('user_notifications').once('value');
  const data = snapshot.val();
  
  const userNotificationMap = new Map();
  
  for (const [key, value] of Object.entries(data)) {
    const uniqueKey = `${value.userId}_${value.notificationId}`;
    
    if (userNotificationMap.has(uniqueKey)) {
      // Duplicate found - keep the newer one
      const existing = userNotificationMap.get(uniqueKey);
      if (value.createdAt > existing.createdAt) {
        await db.ref(`user_notifications/${existing.key}`).remove();
        userNotificationMap.set(uniqueKey, { ...value, key });
      } else {
        await db.ref(`user_notifications/${key}`).remove();
      }
    } else {
      userNotificationMap.set(uniqueKey, { ...value, key });
    }
  }
}
```

## Kết luận

Vấn đề duplicate notifications đã được sửa bằng cách:
1. Cải thiện logic tạo user_notification records
2. Thêm kiểm tra trạng thái trước khi cập nhật
3. Phân biệt public vs targeted notifications
4. Thêm logging để debug

Hệ thống bây giờ sẽ:
- Không tạo duplicate records
- Chỉ cập nhật khi cần thiết
- Xử lý public notifications đúng cách
- Cung cấp logs để debug

Hãy test lại và cho tôi biết kết quả!
