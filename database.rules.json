{"rules": {".read": "auth != null", ".write": "auth != null", "notifications": {".read": "auth != null", ".write": "auth != null", ".indexOn": ["type", "priority", "createdAt", "expiresAt", "status", "isPublic", "scheduledAt"], "$notification_id": {".read": "auth != null", ".write": "auth != null"}}, "user_notifications": {".read": "auth != null", ".write": "auth != null", ".indexOn": ["userId", "notificationId", "isRead", "isSeen", "isDeleted", "createdAt"], "$notificationKey": {".read": "auth != null", ".write": "auth != null"}}, "notification_settings": {".read": "auth != null", ".write": "auth != null", "$userId": {".read": "auth != null", ".write": "auth != null"}}, "notification_stats": {".read": "auth != null", ".write": "auth != null", "$dateKey": {".read": "auth != null", ".write": "auth != null"}}, "bug_reports": {".read": "auth != null", ".write": "auth != null", ".indexOn": ["reportedBy", "status", "createdAt"], "$bugReportId": {".read": "auth != null", ".write": "auth != null"}}, "chat_support": {"$chatId": {".read": "auth != null && (data.child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".write": "auth != null && (data.child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", "messages": {"$messageId": {".write": "auth != null && (data.parent().parent().child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')"}}}}, "seat_reservations": {".read": "auth != null", ".write": "auth != null"}, "user_roles": {"$userId": {".read": "auth != null && (auth.uid == $userId || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".write": "root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer'"}}, "fcm_tokens": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId"}}, "analytics": {".read": "root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer'", ".write": "auth != null"}}}