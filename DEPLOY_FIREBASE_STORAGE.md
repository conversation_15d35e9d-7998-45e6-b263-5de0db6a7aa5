# Hướng dẫn Deploy Firebase Storage Rules

## Bước 1: Cài đặt Firebase CLI

### Windows:
```bash
npm install -g firebase-tools
```

### macOS/Linux:
```bash
curl -sL https://firebase.tools | bash
```

## Bước 2: Đăng nhập Firebase

```bash
firebase login
```

## Bước 3: Khởi tạo Firebase project (nếu chưa có)

```bash
firebase init
```

Chọn:
- ☑ Storage: Configure security rules file for Cloud Storage
- Chọn project: moviefinder-98
- Storage rules file: storage.rules (đã có sẵn)

## Bước 4: Deploy Storage Rules

```bash
firebase deploy --only storage
```

## Bước 5: Kiểm tra deployment

1. Truy cập [Firebase Console](https://console.firebase.google.com/)
2. Chọn project `moviefinder-98`
3. Vào Storage > Rules
4. Kiểm tra rules đã được cập nhật

## Bước 6: Test upload avatar

1. Mở app Flutter
2. <PERSON><PERSON><PERSON> nhập
3. Vào Profile Edit
4. <PERSON><PERSON><PERSON> ảnh và upload
5. Kiểm tra trong Firebase Storage Console

## Troubleshooting

### Lỗi: "Firebase CLI not found"
```bash
npm install -g firebase-tools
```

### Lỗi: "Permission denied"
```bash
firebase login --reauth
```

### Lỗi: "Project not found"
Kiểm tra project ID trong firebase.json:
```json
{
  "projectId": "moviefinder-98"
}
```

### Lỗi: "Storage rules deployment failed"
Kiểm tra syntax trong storage.rules file.

## Xác nhận thành công

Sau khi deploy thành công, bạn sẽ thấy:
```
✔  Deploy complete!

Project Console: https://console.firebase.google.com/project/moviefinder-98/overview
```

## Lưu ý quan trọng

1. **Backup rules cũ**: Firebase tự động backup rules cũ
2. **Test trước khi deploy**: Luôn test rules trong Firebase Console trước
3. **Monitor usage**: Theo dõi usage trong Firebase Console để tránh vượt quota
4. **Security**: Rules hiện tại cho phép authenticated users upload, có thể cần tăng cường bảo mật sau

## Commands hữu ích

```bash
# Xem project hiện tại
firebase projects:list

# Chuyển project
firebase use moviefinder-98

# Deploy tất cả
firebase deploy

# Deploy chỉ storage rules
firebase deploy --only storage

# Xem logs
firebase functions:log
```
