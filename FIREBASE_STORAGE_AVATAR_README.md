# Firebase Storage Avatar Upload Feature

## Tổng quan
Tính năng này cho phép người dùng upload ảnh đại diện thật lên Firebase Storage thay vì sử dụng UI Avatars.

## Các thay đổi đã thực hiện

### 1. Tạo Firebase Storage Service (`lib/services/firebase_storage_service.dart`)
- **uploadUserAvatar()**: Upload ảnh đại diện lên Firebase Storage
- **uploadUserAvatarFromBytes()**: Upload từ bytes (cho web platform)
- **deleteUserAvatar()**: X<PERSON>a ảnh đại diện
- **getUserAvatars()**: <PERSON><PERSON><PERSON> danh sách ảnh của user
- **cleanupOldAvatars()**: Dọn dẹp ảnh cũ (giữ lại 3 ảnh gần nhất)

### 2. Cập nhật Profile Edit Page (`lib/view/page/profile_edit_page.dart`)
- Import FirebaseStorageService
- Thay đổi hàm `_uploadImage()` để upload ảnh thật lên Firebase Storage
- Xử lý lỗi với fallback về UI Avatars nếu upload thất bại
- Hiển thị thông báo tiến trình upload

### 3. Cập nhật Firebase Storage Rules (`storage.rules`)
- Cho phép đọc tất cả file (để hiển thị avatar)
- Cho phép người dùng đã đăng nhập upload/xóa avatar
- Bảo mật cơ bản cho việc truy cập

## Cấu trúc lưu trữ trên Firebase Storage

```
gs://moviefinder-98.firebasestorage.app/
└── user_avatars/
    ├── avatar_user1_1234567890.jpg
    ├── avatar_user1_1234567891.jpg
    └── avatar_user2_1234567892.jpg
```

## Cách hoạt động

1. **Chọn ảnh**: Người dùng chạm vào avatar để chọn ảnh từ camera hoặc thư viện
2. **Xin quyền**: Tự động xin quyền camera và storage
3. **Xử lý ảnh**: Resize ảnh về 512x512 với chất lượng 80%
4. **Upload**: Upload ảnh lên Firebase Storage với tên file unique
5. **Lưu URL**: Lưu download URL vào Firestore user profile
6. **Dọn dẹp**: Tự động xóa ảnh cũ (giữ lại 3 ảnh gần nhất)

## Xử lý lỗi

- Nếu upload thất bại, hệ thống sẽ fallback về UI Avatars
- Hiển thị thông báo lỗi chi tiết cho người dùng
- Không làm crash app khi có lỗi

## Cài đặt và Deploy

### 1. Cài đặt Firebase CLI (nếu chưa có)
```bash
npm install -g firebase-tools
```

### 2. Login vào Firebase
```bash
firebase login
```

### 3. Deploy Storage Rules
```bash
firebase deploy --only storage
```

### 4. Kiểm tra Firebase Storage trong Console
- Truy cập [Firebase Console](https://console.firebase.google.com/)
- Chọn project `moviefinder-98`
- Vào Storage tab để xem các file đã upload

## Testing

### 1. Unit Tests
```bash
flutter test lib/test/firebase_storage_test.dart
```

### 2. Manual Testing
1. Mở app và đăng nhập
2. Vào Profile Edit
3. Chạm vào avatar để chọn ảnh
4. Chọn ảnh từ camera hoặc thư viện
5. Nhấn "Save Changes"
6. Kiểm tra ảnh đã được upload lên Firebase Storage

## Lưu ý quan trọng

### 1. Permissions
- App cần quyền Camera và Storage
- Firebase Storage rules phải cho phép authenticated users upload

### 2. File Size và Format
- Ảnh được resize về 512x512 pixels
- Chất lượng 80% để giảm dung lượng
- Format: JPEG

### 3. Security
- Chỉ user đã đăng nhập mới có thể upload
- Mỗi user chỉ có thể upload avatar của chính họ
- Tự động dọn dẹp ảnh cũ để tiết kiệm storage

### 4. Cost Management
- Firebase Storage có chi phí theo dung lượng và bandwidth
- Tính năng cleanup giúp tiết kiệm chi phí
- Resize ảnh giúp giảm dung lượng

## Troubleshooting

### 1. Upload thất bại
- Kiểm tra internet connection
- Kiểm tra Firebase Storage rules
- Kiểm tra quyền app (camera, storage)

### 2. Ảnh không hiển thị
- Kiểm tra download URL trong Firestore
- Kiểm tra Firebase Storage rules cho read permission

### 3. Lỗi permissions
- Đảm bảo user đã đăng nhập
- Kiểm tra Firebase Authentication

## Tính năng tương lai có thể thêm

1. **Image Compression**: Nén ảnh tốt hơn trước khi upload
2. **Multiple Formats**: Hỗ trợ PNG, WebP
3. **Crop Feature**: Cho phép user crop ảnh trước khi upload
4. **Progress Indicator**: Hiển thị tiến trình upload chi tiết
5. **Offline Support**: Cache ảnh khi offline
