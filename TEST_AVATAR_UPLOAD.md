# Test Avatar Upload - Hướng dẫn

## Bước 1: Hot Reload App
1. Trong terminal VSCode đang chạy app, nhấn `r` để hot reload
2. Hoặc nhấn `R` để hot restart
3. Hoặc restart app hoàn toàn

## Bước 2: Test Upload Avatar
1. **Mở app** (đã đăng nhập với <EMAIL>)
2. **Vào Profile** (tap vào avatar ở góc trên)
3. **Chọn "Edit Profile"**
4. **Tap vào avatar** để chọn ảnh
5. **Chọn "Thư viện ảnh"** hoặc "Camera"
6. **Chọn một ảnh**
7. **Nhấn "Save Changes"**

## Bước 3: Kiểm tra kết quả

### Trong Terminal/Console:
Tìm các log sau:
```
🚀 Starting image picker (bypassing permission check)...
Đang tải ảnh đại diện lên Firebase Storage...
Avatar uploaded to Firebase Storage: https://firebasestorage.googleapis.com/...
Đã tải ảnh đại diện lên thành công!
```

### Trong App:
- Thấy snackbar "Đang tải lên..."
- Thấy snackbar "Đã tải ảnh đại diện lên thành công!"
- Avatar mới hiển thị trong profile

### Trong Firebase Console:
1. Truy cập https://console.firebase.google.com/
2. Chọn project `moviefinder-98`
3. Vào Storage
4. Kiểm tra folder `user_avatars`
5. Thấy file mới: `avatar_[userId]_[timestamp].jpg`

## Nếu gặp lỗi

### Lỗi Permission:
```
PlatformException: Permission denied
```
**Giải pháp:**
1. Vào Settings > Apps > Đớp Phim > Permissions
2. Cấp quyền Camera và Storage/Photos
3. Thử lại

### Lỗi Firebase Storage:
```
[firebase_storage/unauthorized] User does not have permission
```
**Giải pháp:**
1. Deploy Firebase Storage rules:
```bash
firebase deploy --only storage
```

### Lỗi Network:
```
[firebase_storage/unknown] Network error
```
**Giải pháp:**
1. Kiểm tra internet connection
2. Thử lại sau vài giây

## Debug Commands

### Kiểm tra Firebase connection:
```bash
firebase projects:list
```

### Kiểm tra Storage rules:
```bash
firebase deploy --only storage
```

### Xem logs chi tiết:
```bash
flutter logs
```

## Kết quả mong đợi

### Thành công:
1. ✅ Chọn ảnh được
2. ✅ Upload lên Firebase Storage
3. ✅ URL được lưu vào Firestore
4. ✅ Avatar mới hiển thị
5. ✅ File xuất hiện trong Firebase Storage Console

### URL format:
```
https://firebasestorage.googleapis.com/v0/b/moviefinder-98.firebasestorage.app/o/user_avatars%2Favatar_AyhGknRFQqNq5xI96XBA1j246S83_1735689123456.jpg?alt=media&token=...
```

## Troubleshooting nhanh

1. **App crash**: Restart app
2. **Permission denied**: Cấp quyền trong Settings
3. **Upload failed**: Kiểm tra internet và Firebase rules
4. **Avatar không hiển thị**: Kiểm tra URL trong Firestore

## Liên hệ

Nếu vẫn gặp vấn đề, cung cấp:
1. Screenshot lỗi
2. Log từ terminal
3. Steps đã thử
4. Device/emulator info
