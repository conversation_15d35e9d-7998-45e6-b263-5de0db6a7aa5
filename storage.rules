rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Cho phép đọc tất cả file (để hiển thị avatar)
    match /{allPaths=**} {
      allow read: if true;
    }

    // Cho phép người dùng đã đăng nhập upload và xóa avatar
    match /user_avatars/{fileName} {
      allow write, delete: if request.auth != null;
    }

    // Cho phép admin và developer truy cập tất cả (để testing và quản lý)
    match /{allPaths=**} {
      allow write, delete: if request.auth != null;
    }
  }
}
