import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/realtime_bug_report_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/realtime_bug_report_model.dart';
import '../../utils/role_helper.dart';
import '../../utils/app_colors.dart';

class BugReportListPage extends StatefulWidget {
  const BugReportListPage({Key? key}) : super(key: key);

  @override
  State<BugReportListPage> createState() => _BugReportListPageState();
}

class _BugReportListPageState extends State<BugReportListPage>
    with SingleTickerProviderStateMixin {
  final RealtimeBugReportController _controller =
      Get.find<RealtimeBugReportController>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _controller.fetchBugReports();

    // Tạo tab controller dựa trên vai trò người dùng
    final isAdmin = RoleHelper.hasAdminAccess();
    _tabController = TabController(
      length: isAdmin ? 5 : 1,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Báo cáo lỗi',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        bottom: RoleHelper.hasAdminAccess()
            ? TabBar(
                controller: _tabController,
                isScrollable: true,
                tabs: [
                  const Tab(text: 'Tất cả'),
                  Tab(text: 'Chưa nhận (${_getCountByStatus('pending')})'),
                  Tab(text: 'Đã nhận (${_getCountByStatus('accepted')})'),
                  Tab(text: 'Đang fix (${_getCountByStatus('inProgress')})'),
                  Tab(text: 'Đã fix (${_getCountByStatus('fixed')})'),
                ],
              )
            : null,
      ),
      body: SafeArea(
        child: RoleHelper.hasAdminAccess()
            ? TabBarView(
                controller: _tabController,
                children: [
                  _buildBugReportList(null), // Tất cả
                  _buildBugReportList('pending'),
                  _buildBugReportList('accepted'),
                  _buildBugReportList('inProgress'),
                  _buildBugReportList('fixed'),
                ],
              )
            : _buildUserBugReportList(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateBugReportDialog(context),
        backgroundColor: AppColors.buttonPrimary,
        child: const Icon(Icons.add),
      ),
    );
  }

  int _getCountByStatus(String status) {
    return _controller.allBugReports
        .where((report) => report.status == status)
        .length;
  }

  Widget _buildBugReportList(String? status) {
    return Obx(() {
      if (_controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_controller.errorMessage.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _controller.errorMessage,
                style: GoogleFonts.mulish(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _controller.fetchBugReports(),
                child: const Text('Thử lại'),
              ),
            ],
          ),
        );
      }

      final reports = status == null
          ? _controller.allBugReports
          : _controller.allBugReports.where((r) => r.status == status).toList();

      if (reports.isEmpty) {
        return Center(
          child: Text(
            'Không có báo cáo lỗi nào',
            style: GoogleFonts.mulish(
              color: Colors.grey[600],
            ),
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: reports.length,
        itemBuilder: (context, index) {
          return _buildBugReportItem(reports[index]);
        },
      );
    });
  }

  Widget _buildUserBugReportList() {
    return Obx(() {
      if (_controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_controller.errorMessage.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _controller.errorMessage,
                style: GoogleFonts.mulish(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _controller.fetchBugReports(),
                child: const Text('Thử lại'),
              ),
            ],
          ),
        );
      }

      final reports = _controller.userBugReports;

      if (reports.isEmpty) {
        return Center(
          child: Text(
            'Bạn chưa có báo cáo lỗi nào',
            style: GoogleFonts.mulish(
              color: Colors.grey[600],
            ),
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: reports.length,
        itemBuilder: (context, index) {
          return _buildBugReportItem(reports[index]);
        },
      );
    });
  }

  Widget _buildBugReportItem(RealtimeBugReportModel report) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => Get.toNamed(
          '/bug_report_detail',
          parameters: {'bugReportId': report.id},
        ),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      report.title,
                      style: GoogleFonts.mulish(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildStatusChip(report.status),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                report.description,
                style: GoogleFonts.mulish(),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Báo cáo bởi: ${report.reportedByName}',
                    style: GoogleFonts.mulish(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    dateFormat.format(
                        DateTime.fromMillisecondsSinceEpoch(report.createdAt)),
                    style: GoogleFonts.mulish(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              if (report.responses?.isNotEmpty == true) ...[
                const Divider(height: 16),
                Text(
                  '${report.responses?.length ?? 0} phản hồi',
                  style: GoogleFonts.mulish(
                    color: AppColors.primaryBlue,
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color = AppColors.getStatusColor(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        _getStatusDisplayName(status),
        style: GoogleFonts.mulish(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }

  void _showCreateBugReportDialog(BuildContext context) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Báo cáo lỗi mới',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                autofocus: false, // Explicitly disable autofocus
                decoration: const InputDecoration(
                  labelText: 'Tiêu đề',
                  border: OutlineInputBorder(),
                ),
                maxLength: 100,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                autofocus: false, // Explicitly disable autofocus
                decoration: const InputDecoration(
                  labelText: 'Mô tả chi tiết',
                  border: OutlineInputBorder(),
                ),
                maxLines: 5,
                maxLength: 500,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (titleController.text.trim().isEmpty ||
                  descriptionController.text.trim().isEmpty) {
                Get.snackbar(
                  'Lỗi',
                  'Vui lòng nhập đầy đủ thông tin',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              Navigator.pop(context);

              final success = await _controller.createBugReport(
                title: titleController.text.trim(),
                description: descriptionController.text.trim(),
              );

              if (success) {
                Get.snackbar(
                  'Thành công',
                  'Báo cáo lỗi đã được gửi',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonPrimary,
            ),
            child: Text(
              'Gửi',
              style: GoogleFonts.mulish(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'Chưa nhận';
      case 'accepted':
        return 'Đã nhận';
      case 'inProgress':
        return 'Đang fix';
      case 'fixed':
        return 'Đã fix';
      default:
        return status;
    }
  }
}
