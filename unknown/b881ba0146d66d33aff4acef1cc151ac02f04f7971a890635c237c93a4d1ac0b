import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import '../../controllers/auth_controller.dart';
import '../../services/firebase_storage_service.dart';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({Key? key}) : super(key: key);

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  final AuthController _authController = Get.find<AuthController>();
  final FirebaseStorageService _storageService = FirebaseStorageService();
  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  File? _selectedImage;
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Initialize the name controller with the current user's name
    _nameController.text = _authController.user?.name ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      // BYPASS PERMISSION CHECK FOR TESTING
      print('🚀 Starting image picker (bypassing permission check)...');

      // Hiển thị dialog chọn nguồn ảnh
      final ImageSource? source = await _showImageSourceDialog();
      if (source == null) return;

      // Chọn ảnh
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
        preferredCameraDevice: CameraDevice.front,
        requestFullMetadata: false, // Giảm metadata để tránh lỗi format
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });

        Get.snackbar(
          'Thành công',
          'Đã chọn ảnh thành công. Nhấn "Save Changes" để lưu.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      print('Error picking image: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể chọn ảnh: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return await Get.dialog<ImageSource>(
      AlertDialog(
        title: const Text(
          'Chọn nguồn ảnh',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1a1a1a),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt, color: Colors.white),
              title: const Text(
                'Camera',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () => Get.back(result: ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library, color: Colors.white),
              title: const Text(
                'Thư viện ảnh',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () => Get.back(result: ImageSource.gallery),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'Hủy',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      String? photoUrl;

      // Upload ảnh đại diện nếu đã chọn ảnh
      if (_selectedImage != null) {
        try {
          photoUrl = await _uploadImage();
          debugPrint('Photo URL after upload: $photoUrl');
        } catch (error) {
          debugPrint('Error uploading avatar: $error');

          // Nếu có lỗi upload, sử dụng ảnh đại diện mặc định từ tên
          final name = _nameController.text.trim();
          final encodedName = Uri.encodeComponent(name);
          photoUrl =
              'https://ui-avatars.com/api/?name=$encodedName&size=200&background=random';

          debugPrint('Using fallback avatar URL: $photoUrl');

          // Hiển thị thông báo lỗi nhưng vẫn tiếp tục với avatar mặc định
          Get.snackbar(
            'Cảnh báo',
            'Không thể tải ảnh lên, sử dụng avatar mặc định từ tên.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      } else {
        // Nếu không chọn ảnh, giữ nguyên ảnh hiện tại
        photoUrl = _authController.user?.photoUrl;
        debugPrint('Keeping current photo URL: $photoUrl');
      }

      // Cập nhật hồ sơ
      final success = await _authController.updateProfile(
        name: _nameController.text.trim(),
        photoUrl: photoUrl,
      );

      if (success) {
        Get.back();
        Get.snackbar(
          'Thành công',
          'Cập nhật hồ sơ thành công',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        setState(() {
          _errorMessage = _authController.errorMessage;
        });

        // Hiển thị thông báo lỗi
        Get.snackbar(
          'Lỗi',
          'Không thể cập nhật hồ sơ: ${_authController.errorMessage}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Không thể cập nhật hồ sơ: $e';
      });

      // Hiển thị thông báo lỗi
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật hồ sơ: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<String?> _uploadImage() async {
    if (_selectedImage == null) return null;

    try {
      final userId = _authController.user?.id;
      if (userId == null) {
        throw Exception('User not logged in');
      }

      // Hiển thị thông báo đang upload
      Get.snackbar(
        'Đang tải lên',
        'Đang tải ảnh đại diện lên Firebase Storage...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      // Upload ảnh lên Firebase Storage
      final String downloadUrl =
          await _storageService.uploadUserAvatar(userId, _selectedImage!);

      debugPrint('Avatar uploaded to Firebase Storage: $downloadUrl');

      // Dọn dẹp ảnh cũ (giữ lại 3 ảnh gần nhất)
      await _storageService.cleanupOldAvatars(userId);

      // Hiển thị thông báo thành công
      Get.snackbar(
        'Thành công',
        'Đã tải ảnh đại diện lên thành công!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading image to Firebase Storage: $e');

      // Hiển thị thông báo lỗi
      Get.snackbar(
        'Lỗi',
        'Không thể tải ảnh lên: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );

      throw Exception('Failed to upload avatar: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = _authController.user;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Edit Profile',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),

                // Profile image
                GestureDetector(
                  onTap: _pickImage,
                  child: Stack(
                    children: [
                      CircleAvatar(
                        radius: 60,
                        backgroundColor: Colors.white24,
                        backgroundImage: _selectedImage != null
                            ? FileImage(_selectedImage!) as ImageProvider
                            : (user?.photoUrl != null
                                ? NetworkImage(user!.photoUrl!)
                                : null),
                        child:
                            (_selectedImage == null && user?.photoUrl == null)
                                ? Icon(
                                    Icons.person,
                                    size: 60,
                                    color: Colors.white.withOpacity(0.7),
                                  )
                                : null,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),

                // Display name field
                TextFormField(
                  controller: _nameController,
                  autofocus: false, // Explicitly disable autofocus
                  decoration: InputDecoration(
                    labelText: 'Display Name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a display name';
                    }
                    return null;
                  },
                ),

                if (_errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                const SizedBox(height: 30),

                // Save button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _updateProfile,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator()
                        : const Text(
                            'Save Changes',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
