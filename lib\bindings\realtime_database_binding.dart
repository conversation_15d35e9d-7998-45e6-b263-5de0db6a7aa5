import 'package:get/get.dart';
import '../controllers/realtime_notification_controller.dart';
import '../controllers/realtime_bug_report_controller.dart';
import '../controllers/notification_settings_controller.dart';
import '../services/realtime_database_service.dart';

class RealtimeDatabaseBinding extends Bindings {
  @override
  void dependencies() {
    // Service
    Get.lazyPut<RealtimeDatabaseService>(
      () => RealtimeDatabaseService(),
      fenix: true,
    );

    // Controllers
    Get.lazyPut<RealtimeNotificationController>(
      () => RealtimeNotificationController(),
      fenix: true,
    );

    Get.lazyPut<RealtimeBugReportController>(
      () => RealtimeBugReportController(),
      fenix: true,
    );

    Get.lazyPut<NotificationSettingsController>(
      () => NotificationSettingsController(),
      fenix: true,
    );
  }
}
